{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Bullet.ts"], "names": ["_decorator", "Component", "EDITOR", "BulletData", "ObjectPool", "Movable", "BulletSystem", "ccclass", "property", "executeInEditMode", "Bullet", "type", "displayName", "isRunning", "onLoad", "mover", "getComponent", "addComponent", "onBecomeInvisible", "onDestroyBullet", "onCollisionEnter", "other", "self", "onCreate", "isFacingMoveDir", "bulletData", "speed", "speedAngle", "node", "getWorldRotation", "z", "acceleration", "accelerationAngle", "tick", "dt", "destroySelf", "cb", "destroy", "returnNode", "delayDestroy", "scheduleOnce"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CT,U,GAEjD;AACA;AACA;;wBAGaU,M,WAFZH,OAAO,CAAC,QAAD,C,UAIHC,QAAQ,CAAC;AAACG,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,UAGRH,QAAQ,CAAC;AAACG,QAAAA,IAAI;AAAA;AAAA,8BAAL;AAAgBC,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,gBANZH,iB,qBADD,MAEaC,MAFb,SAE4BT,SAF5B,CAEsC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAQ3BY,SAR2B,GAQN,KARM;AAAA;;AAUlCC,QAAAA,MAAM,GAAS;AACX,cAAI,CAAC,KAAKC,KAAV,EAAiB;AACb,iBAAKA,KAAL,GAAa,KAAKC,YAAL;AAAA;AAAA,mCAAb;;AACA,gBAAI,CAAC,KAAKD,KAAV,EAAiB;AACb,mBAAKA,KAAL,GAAa,KAAKE,YAAL;AAAA;AAAA,qCAAb;AACH;AACJ;;AAED,eAAKF,KAAL,CAAWG,iBAAX,GAA+B,MAAM;AACjC;AAAA;AAAA,8CAAaC,eAAb,CAA6B,IAA7B;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACIC,QAAAA,gBAAgB,CAACC,KAAD,EAAcC,IAAd,EAAgC;AAC5C;AACA;AAAA;AAAA,4CAAaH,eAAb,CAA6B,IAA7B;AACH;;AAEMI,QAAAA,QAAQ,GAAS;AACpB,eAAKV,SAAL,GAAiB,IAAjB;AAEA,eAAKE,KAAL,CAAWS,eAAX,GAA6B,KAAKC,UAAL,CAAgBD,eAA7C;AACA,eAAKT,KAAL,CAAWW,KAAX,GAAmB,KAAKD,UAAL,CAAgBC,KAAnC,CAJoB,CAKpB;;AACA,eAAKX,KAAL,CAAWY,UAAX,GAAwB,KAAKC,IAAL,CAAUC,gBAAV,GAA6BC,CAArD;AACA,eAAKf,KAAL,CAAWgB,YAAX,GAA0B,KAAKN,UAAL,CAAgBM,YAA1C;AACA,eAAKhB,KAAL,CAAWiB,iBAAX,GAA+B,KAAKP,UAAL,CAAgBO,iBAA/C;AACH;;AAEMC,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAAA;;AAC1B,cAAI,CAAC,KAAKrB,SAAV,EAAqB,OADK,CAE1B;;AAEA,8BAAKE,KAAL,yBAAYkB,IAAZ,CAAiBC,EAAjB;AACH;;AAEMC,QAAAA,WAAW,GAAS;AACvB,eAAKtB,SAAL,GAAiB,KAAjB;;AACA,gBAAMuB,EAAE,GAAG,MAAM;AACb,gBAAIlC,MAAJ,EAAY;AACR,mBAAK0B,IAAL,CAAUS,OAAV;AACH,aAFD,MAEO;AACH;AAAA;AAAA,4CAAWC,UAAX,CAAsB,KAAKV,IAA3B;AACH;AACJ,WAND;;AAOA,cAAI,KAAKH,UAAL,CAAgBc,YAAhB,GAA+B,CAAnC,EAAsC;AAClC,iBAAKC,YAAL,CAAkB,MAAM;AACpBJ,cAAAA,EAAE;AACL,aAFD,EAEG,KAAKX,UAAL,CAAgBc,YAFnB;AAGH,WAJD,MAIO;AACHH,YAAAA,EAAE;AACL;AACJ;;AAjEiC,O;;;;;iBAGF,I;;;;;;;iBAGR,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletData } from '../../../Game/data/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { Movable } from '../move/Movable';\r\nimport { BulletSystem } from './BulletSystem';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n// 子弹 Bullet 伤害计算 \r\n// Weapon -> 发射器, 喷火, 技能武器, 激光\r\n// WeaponSlot -> SetWeapon\r\n@ccclass('Bullet')\r\n@executeInEditMode\r\nexport class Bullet extends Component {\r\n\r\n    @property({type: BulletData})\r\n    public bulletData: BulletData = null;\r\n\r\n    @property({type: Movable, displayName: \"移动组件\"})\r\n    public mover: Movable = null;\r\n\r\n    public isRunning: boolean = false;\r\n\r\n    onLoad(): void {\r\n        if (!this.mover) {\r\n            this.mover = this.getComponent(Movable);\r\n            if (!this.mover) {\r\n                this.mover = this.addComponent(Movable);\r\n            }\r\n        }\r\n\r\n        this.mover.onBecomeInvisible = () => {\r\n            BulletSystem.onDestroyBullet(this);\r\n        };\r\n    }\r\n\r\n    /**\r\n     * TODO: 如果后续自己写碰撞, 这里要相应进行替换\r\n     */\r\n    onCollisionEnter(other: Node, self: Node): void {\r\n        // 判断另一个node也是子弹或者非子弹, 进行相应处理\r\n        BulletSystem.onDestroyBullet(this);\r\n    }\r\n    \r\n    public onCreate(): void {\r\n        this.isRunning = true;\r\n\r\n        this.mover.isFacingMoveDir = this.bulletData.isFacingMoveDir;\r\n        this.mover.speed = this.bulletData.speed;\r\n        // set speedAngle from current node rotation(note: not in radius)\r\n        this.mover.speedAngle = this.node.getWorldRotation().z;\r\n        this.mover.acceleration = this.bulletData.acceleration;\r\n        this.mover.accelerationAngle = this.bulletData.accelerationAngle;\r\n    }\r\n\r\n    public tick(dt:number) : void {\r\n        if (!this.isRunning) return;\r\n        // Update bullet position\r\n        \r\n        this.mover?.tick(dt);\r\n    }\r\n\r\n    public destroySelf(): void {\r\n        this.isRunning = false;\r\n        const cb = () => {\r\n            if (EDITOR) {\r\n                this.node.destroy();\r\n            } else {\r\n                ObjectPool.returnNode(this.node);\r\n            }\r\n        };\r\n        if (this.bulletData.delayDestroy > 0) {\r\n            this.scheduleOnce(() => {\r\n                cb();\r\n            }, this.bulletData.delayDestroy);\r\n        } else {\r\n            cb();\r\n        }\r\n    }\r\n}\r\n"]}