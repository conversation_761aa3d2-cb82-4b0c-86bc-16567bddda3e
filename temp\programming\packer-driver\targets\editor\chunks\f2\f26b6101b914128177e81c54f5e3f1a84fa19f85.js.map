{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Bullet.ts"], "names": ["_decorator", "Component", "BulletData", "ObjectPool", "ccclass", "property", "Bullet", "type", "isRunning", "onCollisionEnter", "other", "self", "destroySelf", "cb", "CC_EDITOR", "node", "destroy", "returnNode", "bulletData", "delayDestroy", "scheduleOnce"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U,GAE9B;AACA;AACA;;wBAEaM,M,WADZF,OAAO,CAAC,QAAD,C,UAGHC,QAAQ,CAAC;AAACE,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,2BAHb,MACaD,MADb,SAC4BL,SAD5B,CACsC;AAAA;AAAA;;AAAA;;AAAA,eAK3BO,SAL2B;AAAA;;AAMlC;AACJ;AACA;AACIC,QAAAA,gBAAgB,CAACC,KAAD,EAAcC,IAAd,EAAgC,CAC5C;AACH;;AAEDC,QAAAA,WAAW,GAAS;AAChB,eAAKJ,SAAL,GAAiB,KAAjB;;AACA,gBAAMK,EAAE,GAAG,MAAM;AACb,gBAAIC,SAAJ,EAAe;AACX,mBAAKC,IAAL,CAAUC,OAAV;AACH,aAFD,MAEO;AACH;AAAA;AAAA,4CAAWC,UAAX,CAAsB,KAAKF,IAA3B;AACH;AACJ,WAND;;AAOA,cAAI,KAAKG,UAAL,CAAgBC,YAAhB,GAA+B,CAAnC,EAAsC;AAClC,iBAAKC,YAAL,CAAkB,MAAM;AACpBP,cAAAA,EAAE;AACL,aAFD,EAEG,KAAKK,UAAL,CAAgBC,YAFnB;AAGH,WAJD,MAIO;AACHN,YAAAA,EAAE;AACL;AACJ;;AA7BiC,O;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport { BulletData } from '../../../Game/data/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nconst { ccclass, property } = _decorator;\r\n\r\n// 子弹 Bullet 伤害计算 \r\n// Weapon -> 发射器, 喷火, 技能武器, 激光\r\n// WeaponSlot -> SetWeapon\r\n@ccclass('Bullet')\r\nexport class Bullet extends Component {\r\n\r\n    @property({type: BulletData})\r\n    public bulletData: BulletData = null;\r\n\r\n    public isRunning: boolean;\r\n    /**\r\n     * \r\n     */\r\n    onCollisionEnter(other: Node, self: Node): void {\r\n        // 判断另一个node也是子弹或者非子弹, 进行相应处理\r\n    }\r\n\r\n    destroySelf(): void {\r\n        this.isRunning = false;\r\n        const cb = () => {\r\n            if (CC_EDITOR) {\r\n                this.node.destroy();\r\n            } else {\r\n                ObjectPool.returnNode(this.node);\r\n            }\r\n        };\r\n        if (this.bulletData.delayDestroy > 0) {\r\n            this.scheduleOnce(() => {\r\n                cb();\r\n            }, this.bulletData.delayDestroy);\r\n        } else {\r\n            cb();\r\n        }\r\n    }\r\n}\r\n"]}