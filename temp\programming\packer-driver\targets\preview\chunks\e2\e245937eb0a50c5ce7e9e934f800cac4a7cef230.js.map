{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/EmitterData.ts"], "names": ["_decorator", "ccclass", "property", "EmitterData", "displayName"], "mappings": ";;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U;AAE9B;AACA;AACA;AACA;;6BAEaG,W,WADZF,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAGRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAKRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAGRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAGRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAKRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,2BA7Cb,MACaD,WADb,CACyB;AAAA;AAAA;;AAEc;AAFd;;AAIc;AAJd;;AAMc;AANd;;AASc;AATd;;AAWc;AAXd;;AAac;AACnC;AACA;AAfqB;;AAkBc;AAlBd;;AAoBc;AApBd;;AAsBc;AAtBd;;AAwBc;AAxBd;;AA2Bc;AA3Bd;;AA6Bc;AA7Bd;;AA+Bc;AA/Bd;;AAkCc;AAlCd;;AAoCc;AApCd;;AAsCc;AAtCd;;AAwCc;AAEnC;AA1CqB;AAAA,UA6Cc;;;AA7Cd,O;;;;;iBAEM,I;;;;;;;iBAEL,K;;;;;;;iBAEH,I;;;;;;;iBAGK,G;;;;;;;iBAEG,G;;;;;;;;;;;;iBAOH,G;;;;;;;iBAEA,G;;;;;;;iBAEH,G;;;;;;;iBAEG,G;;;;;;;iBAGA,C;;;;;;;iBAEG,G;;;;;;;iBAED,G;;;;;;;iBAGT,CAAC,E;;;;;;;iBAED,C;;;;;;;iBAEA,E;;;;;;;iBAEC,G", "sourcesContent": ["\r\nimport { _decorator, error, v2, Vec2 } from \"cc\";\r\nimport { Tools } from \"../utils/Tools\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 发射器数据\r\n * 所有时间相关的，单位都是秒(s)\r\n */\r\n@ccclass(\"EmitterData\")\r\nexport class EmitterData {\r\n    @property({displayName: '是否仅在屏幕内发射'})\r\n    isOnlyInScreen : boolean = true;   // 仅在屏幕内才发射\r\n    @property({displayName: '是否预热'})\r\n    isPreWarm : boolean = false;       // 是否预热\r\n    @property({displayName: '是否循环'})\r\n    isLoop : boolean = true;           // 是否循环\r\n\r\n    @property({displayName: '初始延迟'})\r\n    initialDelay : number = 0.0;       // 初始延迟\r\n    @property({displayName: '预热持续时长'})\r\n    preWarmDuration : number = 0.0;    // 预热持续时长\r\n    @property({displayName: '预热特效'})\r\n    preWarmEffect : string;            // 预热特效:(这个是否用prefab，直接包含音效、音量等信息)\r\n    // @property({displayName: '预热音效'})\r\n    // preWarmSound : string;             // 预热音效\r\n\r\n    @property({displayName: '发射器持续时间'})\r\n    emitDuration : number = 1.0;       // 发射器持续时间\r\n    @property({displayName: '发射间隔'})\r\n    emitInterval : number = 1.0;       // 发射间隔\r\n    @property({displayName: '发射速度'})\r\n    emitPower : number = 1.0;          // 用来修改子弹初始速度的乘数(备用)\r\n    @property({displayName: '循环间隔'})\r\n    loopInterval : number = 0.0;       // 循环间隔\r\n\r\n    @property({displayName: '循环次数'})\r\n    perEmitCount : number = 1;         // 单次发射数量\r\n    @property({displayName: '单次发射多个子弹时的间隔'})\r\n    perEmitInterval : number = 0.0;    // 单次发射多个子弹时的间隔\r\n    @property({displayName: '单次发射多个子弹时的x偏移'})\r\n    perEmitOffsetX : number = 0.0;     // 单次发射多个子弹时的x偏移\r\n\r\n    @property({displayName: '发射角度'})\r\n    angle : number = -90;              // 发射角度: -90朝下\r\n    @property({displayName: '发射条数'})\r\n    count : number = 1;                // 发射条数(弹道数量)\r\n    @property({displayName: '发射范围'})\r\n    arc   : number = 60;               // 发射范围(弧度范围)\r\n    @property({displayName: '发射半径'})\r\n    radius : number = 1.0;             // 发射半径\r\n\r\n    // TODO: 参数随机化\r\n    \r\n    @property({displayName: '发射特效'})\r\n    emitEffect : string;               // 发射特效(多个的话建议做到prefab上?) 包含音效?\r\n}"]}