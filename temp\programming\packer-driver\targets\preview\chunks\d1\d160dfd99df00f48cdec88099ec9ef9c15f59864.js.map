{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/move/Movable.ts"], "names": ["_decorator", "Component", "Vec3", "ccclass", "property", "executeInEditMode", "Movable", "isFacingMoveDir", "speed", "speedAngle", "acceleration", "accelerationAngle", "isVisible", "onBecomeVisible", "onBecomeInvisible", "activeModifiers", "Map", "addSolver", "solver", "set", "targetType", "tick", "dt", "for<PERSON>ach", "key", "lastPos", "node", "getPosition", "velocityX", "Math", "cos", "PI", "velocityY", "sin", "accelerationX", "accelerationY", "newVelocityX", "newVelocityY", "sqrt", "atan2", "newPos", "x", "y", "z", "setPosition", "setRotationFromEuler", "isFinished", "delete", "checkVisibility", "setVisible", "visible"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;;;;;;;;;OAChC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CL,U;;yBAKpCM,O,WAFZH,OAAO,CAAC,SAAD,C,gBACPE,iB,UADD,MAEaC,OAFb,SAE6BL,SAF7B,CAE2D;AAAA;AAAA;AAAA,eAEhDM,eAFgD,GAErB,KAFqB;AAEd;AAFc,eAGhDC,KAHgD;AAGjB;AAHiB,eAIhDC,UAJgD;AAIjB;AAJiB,eAKhDC,YALgD;AAKjB;AALiB,eAMhDC,iBANgD;AAMjB;AANiB,eAQ/CC,SAR+C,GAQ1B,KAR0B;AAQjB;AARiB,eAShDC,eATgD,GASb,IATa;AAAA,eAUhDC,iBAVgD,GAUX,IAVW;AAYvD;AAZuD,eAa/CC,eAb+C,GAaM,IAAIC,GAAJ,EAbN;AAAA;;AAehDC,QAAAA,SAAS,CAACC,MAAD,EAAwB;AACpC,eAAKH,eAAL,CAAqBI,GAArB,CAAyBD,MAAM,CAACE,UAAhC,EAA4CF,MAA5C;AACH;;AAEMG,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,eAAKP,eAAL,CAAqBQ,OAArB,CAA6B,CAACL,MAAD,EAASM,GAAT,KACzBN,MAAM,CAACG,IAAP,CAAY,IAAZ,EAAkBC,EAAlB,CADJ,EAD0B,CAK1B;;AACA,cAAMG,OAAO,GAAG,KAAKC,IAAL,CAAUC,WAAV,EAAhB,CAN0B,CAQ1B;;AACA,cAAMC,SAAS,GAAG,KAAKpB,KAAL,GAAaqB,IAAI,CAACC,GAAL,CAAS,KAAKrB,UAAL,GAAkBoB,IAAI,CAACE,EAAvB,GAA4B,GAArC,CAA/B;AACA,cAAMC,SAAS,GAAG,KAAKxB,KAAL,GAAaqB,IAAI,CAACI,GAAL,CAAS,KAAKxB,UAAL,GAAkBoB,IAAI,CAACE,EAAvB,GAA4B,GAArC,CAA/B,CAV0B,CAY1B;;AACA,cAAMG,aAAa,GAAG,KAAKxB,YAAL,GAAoBmB,IAAI,CAACC,GAAL,CAAS,KAAKnB,iBAAL,GAAyBkB,IAAI,CAACE,EAA9B,GAAmC,GAA5C,CAA1C;AACA,cAAMI,aAAa,GAAG,KAAKzB,YAAL,GAAoBmB,IAAI,CAACI,GAAL,CAAS,KAAKtB,iBAAL,GAAyBkB,IAAI,CAACE,EAA9B,GAAmC,GAA5C,CAA1C,CAd0B,CAgB1B;;AACA,cAAMK,YAAY,GAAGR,SAAS,GAAGM,aAAa,GAAGZ,EAAjD;AACA,cAAMe,YAAY,GAAGL,SAAS,GAAGG,aAAa,GAAGb,EAAjD,CAlB0B,CAoB1B;;AACA,eAAKd,KAAL,GAAaqB,IAAI,CAACS,IAAL,CAAUF,YAAY,GAAGA,YAAf,GAA8BC,YAAY,GAAGA,YAAvD,CAAb;AACA,eAAK5B,UAAL,GAAkBoB,IAAI,CAACU,KAAL,CAAWF,YAAX,EAAyBD,YAAzB,IAAyC,GAAzC,GAA+CP,IAAI,CAACE,EAAtE,CAtB0B,CAwB1B;;AACA,cAAMS,MAAM,GAAG,IAAItC,IAAJ,CACXuB,OAAO,CAACgB,CAAR,GAAYL,YAAY,GAAGd,EADhB,EAEXG,OAAO,CAACiB,CAAR,GAAYL,YAAY,GAAGf,EAFhB,EAGXG,OAAO,CAACkB,CAHG,CAAf;AAKA,eAAKjB,IAAL,CAAUkB,WAAV,CAAsBJ,MAAtB;;AAEA,cAAI,KAAKjC,eAAT,EAA0B;AACtB,iBAAKmB,IAAL,CAAUmB,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAKpC,UAA1C;AACH,WAlCyB,CAoC1B;;;AACA,eAAKM,eAAL,CAAqBQ,OAArB,CAA6B,CAACL,MAAD,EAASM,GAAT,KAAiB;AAC1C,gBAAIN,MAAM,CAAC4B,UAAP,EAAJ,EAAyB;AACrB,mBAAK/B,eAAL,CAAqBgC,MAArB,CAA4BvB,GAA5B;AACH;AACJ,WAJD;AAKH;AAED;AACJ;AACA;;;AACWwB,QAAAA,eAAe,GAAS;AAC3B,cAAMpC,SAAS,GAAG,IAAlB,CAD2B,CACJ;;AACvB,eAAKqC,UAAL,CAAgBrC,SAAhB;AACH;;AAEMqC,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,cAAI,KAAKtC,SAAL,KAAmBsC,OAAvB,EAAgC;AAEhC,eAAKtC,SAAL,GAAiBsC,OAAjB;;AACA,cAAIA,OAAO,IAAI,KAAKrC,eAApB,EAAqC;AACjC,iBAAKA,eAAL;AACH,WAFD,MAEO,IAAI,CAACqC,OAAD,IAAY,KAAKpC,iBAArB,EAAwC;AAC3C,iBAAKA,iBAAL;AACH;AACJ;;AAhFsD,O", "sourcesContent": ["import { _decorator, Component, Node, Vec3 } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { IMovable, IMoveModifier, eMoveModifier } from './IMovable';\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    public isFacingMoveDir: boolean = false; // 是否朝向行进方向\r\n    public speed: number;                 // 速度\r\n    public speedAngle: number;            // 速度方向 (用角度表示)\r\n    public acceleration: number;          // 加速度\r\n    public accelerationAngle: number;     // 加速度方向 (用角度表示)\r\n\r\n    private isVisible: boolean = false;   // 是否可见\r\n    public onBecomeVisible: Function | null = null;\r\n    public onBecomeInvisible: Function | null = null;\r\n\r\n    // make a map by eMoveModifier\r\n    private activeModifiers: Map<eMoveModifier, IMoveModifier> = new Map();\r\n\r\n    public addSolver(solver: IMoveModifier) {\r\n        this.activeModifiers.set(solver.targetType, solver);\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        this.activeModifiers.forEach((solver, key) => \r\n            solver.tick(this, dt)\r\n        );\r\n\r\n        // 根据移动属性更新位置\r\n        const lastPos = this.node.getPosition();\r\n\r\n        // Convert speed and angle to velocity vector\r\n        const velocityX = this.speed * Math.cos(this.speedAngle * Math.PI / 180);\r\n        const velocityY = this.speed * Math.sin(this.speedAngle * Math.PI / 180);\r\n\r\n        // Convert acceleration and angle to acceleration vector\r\n        const accelerationX = this.acceleration * Math.cos(this.accelerationAngle * Math.PI / 180);\r\n        const accelerationY = this.acceleration * Math.sin(this.accelerationAngle * Math.PI / 180);\r\n\r\n        // Update velocity vector: v = v + a * dt\r\n        const newVelocityX = velocityX + accelerationX * dt;\r\n        const newVelocityY = velocityY + accelerationY * dt;\r\n\r\n        // Convert back to speed and angle\r\n        this.speed = Math.sqrt(newVelocityX * newVelocityX + newVelocityY * newVelocityY);\r\n        this.speedAngle = Math.atan2(newVelocityY, newVelocityX) * 180 / Math.PI;\r\n\r\n        // Update position: p = p + v * dt\r\n        const newPos = new Vec3(\r\n            lastPos.x + newVelocityX * dt,\r\n            lastPos.y + newVelocityY * dt,\r\n            lastPos.z\r\n        );\r\n        this.node.setPosition(newPos);\r\n\r\n        if (this.isFacingMoveDir) {\r\n            this.node.setRotationFromEuler(0, 0, this.speedAngle);\r\n        }\r\n\r\n        // remove finished solvers\r\n        this.activeModifiers.forEach((solver, key) => {\r\n            if (solver.isFinished()) {\r\n                this.activeModifiers.delete(key);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * TODO: 如果后续自己写碰撞，这里可以直接查询格子坐标来获取当前是否在屏幕内\r\n     */\r\n    public checkVisibility(): void {\r\n        const isVisible = true;// && this.node.getWorldBoundingBox().intersects(this.node.getScene().getBoundingBox());\r\n        this.setVisible(isVisible);\r\n    }\r\n\r\n    public setVisible(visible: boolean) {\r\n        if (this.isVisible === visible) return;\r\n\r\n        this.isVisible = visible;\r\n        if (visible && this.onBecomeVisible) {\r\n            this.onBecomeVisible();\r\n        } else if (!visible && this.onBecomeInvisible) {\r\n            this.onBecomeInvisible();\r\n        }\r\n    }\r\n}"]}