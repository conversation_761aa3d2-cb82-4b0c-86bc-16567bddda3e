import { _decorator, Vec3, Node } from "cc";
import { Bullet } from "./Bullet";
import { Emitter } from "./Emitter";
const { ccclass } = _decorator;

/**
 * BulletSystem - manages all bullets in the game world
 * Handles bullet creation, movement, collision, and cleanup
 */
export class BulletSystem {

    /**
     * All active bullets
     */
    public static allBullets: Bullet[] = [];

    /**
     * All active emitters
     */
    public static allEmitters: Emitter[] = [];

    // public static isEmitterEnabled: boolean = true;
    // public static isBulletEnabled: boolean = true;
    public static bulletParent: Node;

    
}
