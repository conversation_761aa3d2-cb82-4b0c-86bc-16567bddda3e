import { _decorator, error, v2, Vec2 } from "cc";
import { Tools } from "../utils/Tools";
const { ccclass, property } = _decorator;

/**
 * 子弹数据
 * 所有时间相关的，单位都是秒(s)
 */
@ccclass("BulletData")
export class BulletData {
    @property({displayName: '是否朝向行进方向'})
    isFacingMoveDir : boolean = false;        // 是否朝向行进方向
    @property({displayName: '是否可被破坏'})
    isDestructive : boolean = false;          // 是否可被破坏
    @property({displayName: '命中时是否被销毁'})
    isDestructiveOnHit : boolean = false;     // 命中时是否被销毁

    // @property({displayName: '子弹Prefab'})
    // prefab : string;                          // 子弹Prefab: 考虑包含拖尾特效、颜色、缩放等
    @property({displayName: '子弹持续时间'})
    duration : number = 10;                   // 子弹持续时间(超出后销毁回收)
    @property({displayName: '子弹伤害'})
    damage : number = 1;                      // 子弹伤害
    @property({displayName: '子弹速度'})
    speed : number = 1;                       // 子弹速度
    @property({displayName: '子弹加速度'})
    accelerate : number = 0;                  // 子弹加速度
    @property({displayName: '加速度方向'})
    accelerateAngle : number = 0;             // 加速度方向(角度)

    @property({displayName: '延迟销毁时间'})
    delayDestroy : number = 0;                 // 延迟销毁时间
}
