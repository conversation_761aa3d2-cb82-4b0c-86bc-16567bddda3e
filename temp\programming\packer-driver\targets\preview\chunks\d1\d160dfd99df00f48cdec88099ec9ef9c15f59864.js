System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec3, _dec, _class, _crd, ccclass, property, executeInEditMode, Movable;

  function _reportPossibleCrUseOfIMovable(extras) {
    _reporterNs.report("IMovable", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIMoveModifier(extras) {
    _reporterNs.report("IMoveModifier", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeMoveModifier(extras) {
    _reporterNs.report("eMoveModifier", "./IMovable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec3 = _cc.Vec3;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "69d1c91vGlPmascxGbqaXaG", "Movable", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Vec3']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("Movable", Movable = (_dec = ccclass('Movable'), _dec(_class = executeInEditMode(_class = class Movable extends Component {
        constructor() {
          super(...arguments);
          this.speed = void 0;
          // 速度
          this.speedAngle = void 0;
          // 速度方向 (用角度表示)
          this.acceleration = void 0;
          // 加速度
          this.accelerationAngle = void 0;
          // 加速度方向 (用角度表示)
          // make a map by eMoveModifier
          this.activeModifiers = new Map();
        }

        addSolver(solver) {
          this.activeModifiers.set(solver.targetType, solver);
        }

        tick(dt) {
          this.activeModifiers.forEach((solver, key) => solver.tick(this, dt)); // 根据移动属性更新位置

          var lastPos = this.node.getPosition(); // update speed and acceleration

          this.speed += this.acceleration * dt;
          this.speedAngle += this.accelerationAngle * dt;
          var newPos = new Vec3(lastPos.x + this.speed * Math.cos(this.speedAngle * Math.PI / 180) * dt, lastPos.y + this.speed * Math.sin(this.speedAngle * Math.PI / 180) * dt, lastPos.z);
          this.node.setPosition(newPos); // remove finished solvers

          this.activeModifiers.forEach((solver, key) => {
            if (solver.isFinished()) {
              this.activeModifiers.delete(key);
            }
          });
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d160dfd99df00f48cdec88099ec9ef9c15f59864.js.map