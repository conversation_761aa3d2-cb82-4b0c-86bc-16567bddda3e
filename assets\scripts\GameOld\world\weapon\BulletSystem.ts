import { _decorator, find, Vec3, Node } from "cc";
import { Bullet } from "./Bullet";
import { Emitter } from "./Emitter";
const { ccclass } = _decorator;

/**
 * BulletSystem - manages all bullets in the game world
 * Handles bullet creation, movement, collision, and cleanup
 */
export class BulletSystem {

    /**
     * All active bullets
     */
    public static allBullets: Bullet[] = [];

    /**
     * All active emitters
     */
    public static allEmitters: Emitter[] = [];

    public static bulletParentPath: string = 'Canvas/GameUI/bullet_root';
    // public static isEmitterEnabled: boolean = true;
    // public static isBulletEnabled: boolean = true;
    public static bulletParent: Node;

    public static onCreateEmitter(emitter:Emitter) {
        for (let i = 0; i < this.allEmitters.length; i++) {
            if (this.allEmitters[i] === emitter) {
                return;
            }
        }

        this.allEmitters.push(emitter);

        if (!this.bulletParent || !this.bulletParent.isValid) {
            if (this.bulletParentPath.length > 0) {
                this.bulletParent = find(this.bulletParentPath);
                if (!this.bulletParent) {
                    console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);
                    this.bulletParent = emitter.node;
                }
            }
        }
    }

    public static onDestroyEmitter(emitter:Emitter) {
        this.allEmitters = this.allEmitters.filter(e => e !== emitter);
    }

    public static onCreateBullet(bullet: Bullet) {
        for (let i = 0; i < this.allBullets.length; i++) {
            if (this.allBullets[i] === bullet) {
                return;
            }
        }

        bullet.onCreate();
        this.allBullets.push(bullet);
        bullet.node.setParent(this.bulletParent, true);
    }

    public static onDestroyBullet(bullet: Bullet) {
        this.allBullets = this.allBullets.filter(b => b !== bullet);
    }

    public static tickEmitters(dt:number) {
        for (const emitter of this.allEmitters) {
            emitter.tick(dt);
        }
    }

    public static tickBullets(dt:number) {
        for (const bullet of this.allBullets) {
            bullet.tick(dt);
        }
    }

    public static destroyAllBullets() {
        for (const bullet of this.allBullets) {
            bullet.destroySelf();
        }
        this.allBullets = [];
    }
}
