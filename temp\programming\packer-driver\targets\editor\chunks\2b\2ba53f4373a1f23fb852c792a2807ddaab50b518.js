System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, eMoveModifier, eEasing, _dec, _class, _crd, ccclass, property, DefaultMoveModifier;

  function _reportPossibleCrUseOfIMovable(extras) {
    _reporterNs.report("IMovable", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIMoveModifier(extras) {
    _reporterNs.report("IMoveModifier", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeMoveModifier(extras) {
    _reporterNs.report("eMoveModifier", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEasing(extras) {
    _reporterNs.report("eEasing", "./IMovable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      eMoveModifier = _unresolved_2.eMoveModifier;
      eEasing = _unresolved_2.eEasing;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "20141BI6rtLN5mVezcyMiyX", "DefaultMoveModifier", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("DefaultMoveModifier", DefaultMoveModifier = (_dec = ccclass('DefaultMoveModifier'), _dec(_class = class DefaultMoveModifier {
        constructor(targetType, targetValue, duration, easing = (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
          error: Error()
        }), eEasing) : eEasing).Linear) {
          this.targetType = (_crd && eMoveModifier === void 0 ? (_reportPossibleCrUseOfeMoveModifier({
            error: Error()
          }), eMoveModifier) : eMoveModifier).Speed;
          this.targetValue = 1.0;
          this.duration = 1.0;
          this.easing = (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
            error: Error()
          }), eEasing) : eEasing).Linear;
          this.startValue = 0;
          this.currentTime = 0;
          this.isInitialized = false;
          this.targetType = targetType;
          this.targetValue = targetValue;
          this.duration = duration;
          this.easing = easing;
        }

        isFinished() {
          return this.currentTime >= this.duration;
        }

        initialize(movable) {
          switch (this.targetType) {
            case (_crd && eMoveModifier === void 0 ? (_reportPossibleCrUseOfeMoveModifier({
              error: Error()
            }), eMoveModifier) : eMoveModifier).Speed:
              this.startValue = movable.speed;
              break;

            case (_crd && eMoveModifier === void 0 ? (_reportPossibleCrUseOfeMoveModifier({
              error: Error()
            }), eMoveModifier) : eMoveModifier).SpeedAngle:
              this.startValue = movable.speedAngle;
              break;

            case (_crd && eMoveModifier === void 0 ? (_reportPossibleCrUseOfeMoveModifier({
              error: Error()
            }), eMoveModifier) : eMoveModifier).Acceleration:
              this.startValue = movable.acceleration;
              break;

            case (_crd && eMoveModifier === void 0 ? (_reportPossibleCrUseOfeMoveModifier({
              error: Error()
            }), eMoveModifier) : eMoveModifier).AccelerationAngle:
              this.startValue = movable.accelerationAngle;
              break;
          }

          this.isInitialized = true;
        }

        tick(movable, deltaTime) {
          // Initialize start value on first tick
          if (!this.isInitialized) {
            this.initialize(movable);
          } // Update the appropriate property based on targetType


          switch (this.targetType) {
            case (_crd && eMoveModifier === void 0 ? (_reportPossibleCrUseOfeMoveModifier({
              error: Error()
            }), eMoveModifier) : eMoveModifier).Speed:
              movable.speed = this.tickMoveProperty(movable.speed, deltaTime);
              break;

            case (_crd && eMoveModifier === void 0 ? (_reportPossibleCrUseOfeMoveModifier({
              error: Error()
            }), eMoveModifier) : eMoveModifier).SpeedAngle:
              movable.speedAngle = this.tickMoveProperty(movable.speedAngle, deltaTime);
              break;

            case (_crd && eMoveModifier === void 0 ? (_reportPossibleCrUseOfeMoveModifier({
              error: Error()
            }), eMoveModifier) : eMoveModifier).Acceleration:
              movable.acceleration = this.tickMoveProperty(movable.acceleration, deltaTime);
              break;

            case (_crd && eMoveModifier === void 0 ? (_reportPossibleCrUseOfeMoveModifier({
              error: Error()
            }), eMoveModifier) : eMoveModifier).AccelerationAngle:
              movable.accelerationAngle = this.tickMoveProperty(movable.accelerationAngle, deltaTime);
              break;
          }
        }

        tickMoveProperty(currentValue, deltaTime) {
          // Update current time
          this.currentTime += deltaTime; // Calculate progress ratio (0 to 1)

          let progress = Math.min(1.0, this.currentTime / this.duration); // Apply easing function

          let easedProgress = DefaultMoveModifier.applyEasing(this.easing, progress); // Interpolate between start value and target value

          return this.startValue + (this.targetValue - this.startValue) * easedProgress;
        }

        static applyEasing(easing, t) {
          switch (easing) {
            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).Linear:
              return t;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InSine:
              return 1 - Math.cos(t * Math.PI / 2);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).OutSine:
              return Math.sin(t * Math.PI / 2);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InOutSine:
              return -(Math.cos(Math.PI * t) - 1) / 2;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InQuad:
              return t * t;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).OutQuad:
              return 1 - (1 - t) * (1 - t);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InOutQuad:
              return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;

            default:
              return t;
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2ba53f4373a1f23fb852c792a2807ddaab50b518.js.map