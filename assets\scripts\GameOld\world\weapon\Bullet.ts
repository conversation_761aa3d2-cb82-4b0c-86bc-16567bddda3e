import { _decorator, Component, Node } from 'cc';
import { BulletData } from '../../../Game/data/BulletData';
const { ccclass, property } = _decorator;

// 子弹 Bullet 伤害计算 
// Weapon -> 发射器, 喷火, 技能武器, 激光
// WeaponSlot -> SetWeapon
@ccclass('Bullet')
export class Bullet extends Component {

    @property({type: BulletData})
    public bulletData: BulletData = null;

    public isRunning: boolean;
    /**
     * 
     */
    onCollisionEnter(other: Node, self: Node): void {
        // 判断另一个node也是子弹或者非子弹, 进行相应处理
    }

    destroySelf(): void {
        this.isRunning = false;
        const cb = () => {
            if (CC_EDITOR) {
                this.node.destroy();
            } else {
                ObjectPool.push_node_to_pool(this.node);
            }
        };
        if (destroy_time > 0) {
            this.scheduleOnce(() => {
                cb();
            }, destroy_time);
        } else {
            cb();
        }
        this.node.destroy();
    }
}
