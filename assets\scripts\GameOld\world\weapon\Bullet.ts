import { _decorator, misc, Component, Node } from 'cc';
import { EDITOR } from 'cc/env';
import { BulletData } from '../../../Game/data/BulletData';
import { ObjectPool } from './ObjectPool';
import { Movable } from '../move/Movable';
import { BulletSystem } from './BulletSystem';
const { ccclass, property, executeInEditMode } = _decorator;
const { degreesToRadians, radiansToDegrees } = misc;

// 子弹 Bullet 伤害计算 
// Weapon -> 发射器, 喷火, 技能武器, 激光
// WeaponSlot -> SetWeapon
@ccclass('Bullet')
@executeInEditMode
export class Bullet extends Component {

    @property({type: BulletData})
    public bulletData: BulletData = null;

    @property({type: Movable, displayName: "移动组件"})
    public mover: Movable = null;

    public isRunning: boolean = false;

    onLoad(): void {
        if (!this.mover) {
            this.mover = this.getComponent(Movable);
            if (!this.mover) {
                this.mover = this.addComponent(Movable);
            }
        }

        this.mover.onBecomeInvisible = () => {
            BulletSystem.onDestroyBullet(this);
        };
    }

    /**
     * TODO: 如果后续自己写碰撞, 这里要相应进行替换
     */
    onCollisionEnter(other: Node, self: Node): void {
        // 判断另一个node也是子弹或者非子弹, 进行相应处理
        BulletSystem.onDestroyBullet(this);
    }
    
    public onCreate(): void {
        this.isRunning = true;

        this.mover.isFacingMoveDir = this.bulletData.isFacingMoveDir;
        this.mover.speed = this.bulletData.speed;
        // speedAngle is set in Emitter
        // this.mover.speedAngle = this.node.eulerAngles.z;
        this.mover.acceleration = this.bulletData.acceleration;
        this.mover.accelerationAngle = this.bulletData.accelerationAngle;
    }

    public tick(dt:number) : void {
        if (!this.isRunning) return;
        // Update bullet position
        
        this.mover?.tick(dt);
    }

    public destroySelf(): void {
        this.isRunning = false;
        const cb = () => {
            if (EDITOR) {
                this.node.destroy();
            } else {
                ObjectPool.returnNode(this.node);
            }
        };
        if (this.bulletData.delayDestroy > 0) {
            this.scheduleOnce(() => {
                cb();
            }, this.bulletData.delayDestroy);
        } else {
            cb();
        }
    }
}
