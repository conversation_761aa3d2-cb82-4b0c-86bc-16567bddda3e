System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _crd, ccclass, property, BulletData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "be0b4sF0ulLq5+nHWXeK6jD", "BulletData", undefined);

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 子弹数据
       * 所有时间相关的，单位都是秒(s)
       */

      _export("BulletData", BulletData = (_dec = ccclass("BulletData"), _dec2 = property({
        displayName: '是否朝向行进方向'
      }), _dec3 = property({
        displayName: '是否可被破坏'
      }), _dec4 = property({
        displayName: '命中时是否被销毁'
      }), _dec5 = property({
        displayName: '子弹持续时间'
      }), _dec6 = property({
        displayName: '子弹伤害'
      }), _dec7 = property({
        displayName: '子弹速度'
      }), _dec8 = property({
        displayName: '子弹加速度'
      }), _dec9 = property({
        displayName: '加速度方向'
      }), _dec10 = property({
        displayName: '延迟销毁时间'
      }), _dec(_class = (_class2 = class BulletData {
        constructor() {
          _initializerDefineProperty(this, "isFacingMoveDir", _descriptor, this);

          // 是否朝向行进方向
          _initializerDefineProperty(this, "isDestructive", _descriptor2, this);

          // 是否可被破坏
          _initializerDefineProperty(this, "isDestructiveOnHit", _descriptor3, this);

          // 命中时是否被销毁
          // @property({displayName: '子弹Prefab'})
          // prefab : string;                          // 子弹Prefab: 考虑包含拖尾特效、颜色、缩放等
          _initializerDefineProperty(this, "duration", _descriptor4, this);

          // 子弹持续时间(超出后销毁回收)
          _initializerDefineProperty(this, "damage", _descriptor5, this);

          // 子弹伤害
          _initializerDefineProperty(this, "speed", _descriptor6, this);

          // 子弹速度
          _initializerDefineProperty(this, "acceleration", _descriptor7, this);

          // 子弹加速度
          _initializerDefineProperty(this, "accelerationAngle", _descriptor8, this);

          // 加速度方向(角度)
          _initializerDefineProperty(this, "delayDestroy", _descriptor9, this);
        } // 延迟销毁时间


      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "isFacingMoveDir", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "isDestructive", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "isDestructiveOnHit", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "duration", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 10;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "damage", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "acceleration", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "accelerationAngle", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "delayDestroy", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=784216da291ec591f0ed636545368fbe35d9a493.js.map