import { _decorator, Component, Node, Vec3 } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;
import { IMovable, IMoveModifier, eMoveModifier } from './IMovable';

@ccclass('Movable')
@executeInEditMode
export class Movable extends Component implements IMovable {

    public speed: number;                 // 速度
    public speedAngle: number;            // 速度方向 (用角度表示)
    public acceleration: number;          // 加速度
    public accelerationAngle: number;     // 加速度方向 (用角度表示)

    private isVisible: boolean = false;   // 是否可见
    public onBecomeVisible: Function | null = null;
    public onBecomeInvisible: Function | null = null;

    // make a map by eMoveModifier
    private activeModifiers: Map<eMoveModifier, IMoveModifier> = new Map();

    public addSolver(solver: IMoveModifier) {
        this.activeModifiers.set(solver.targetType, solver);
    }

    public tick(dt: number): void {
        this.activeModifiers.forEach((solver, key) => 
            solver.tick(this, dt)
        );

        // 根据移动属性更新位置
        const lastPos = this.node.getPosition();

        // update speed and acceleration
        this.speed += this.acceleration * dt;
        this.speedAngle += this.accelerationAngle * dt;

        console.log(`Speed: ${this.speed}, SpeedAngle: ${this.speedAngle}`);

        const newPos = new Vec3(
            lastPos.x + this.speed * Math.cos(this.speedAngle * Math.PI / 180) * dt,
            lastPos.y + this.speed * Math.sin(this.speedAngle * Math.PI / 180) * dt,
            lastPos.z
        );
        this.node.setPosition(newPos);

        // remove finished solvers
        this.activeModifiers.forEach((solver, key) => {
            if (solver.isFinished()) {
                this.activeModifiers.delete(key);
            }
        });
    }

    /**
     * TODO: 如果后续自己写碰撞，这里可以直接查询格子坐标来获取当前是否在屏幕内
     */
    public checkVisibility(): void {
        const isVisible = true;// && this.node.getWorldBoundingBox().intersects(this.node.getScene().getBoundingBox());
        this.setVisible(isVisible);
    }

    public setVisible(visible: boolean) {
        if (this.isVisible === visible) return;

        this.isVisible = visible;
        if (visible && this.onBecomeVisible) {
            this.onBecomeVisible();
        } else if (!visible && this.onBecomeInvisible) {
            this.onBecomeInvisible();
        }
    }
}