System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, find, BulletSystem, _crd, ccclass;

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  _export("BulletSystem", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      find = _cc.find;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7cb83oXVZVMBJYZY7lJNPxV", "BulletSystem", undefined);

      __checkObsolete__(['_decorator', 'find', 'Vec3', 'Node']);

      ({
        ccclass
      } = _decorator);
      /**
       * BulletSystem - manages all bullets in the game world
       * Handles bullet creation, movement, collision, and cleanup
       */

      _export("BulletSystem", BulletSystem = class BulletSystem {
        static onCreateEmitter(emitter) {
          for (let i = 0; i < this.allEmitters.length; i++) {
            if (this.allEmitters[i] === emitter) {
              return;
            }
          }

          this.allEmitters.push(emitter);

          if (!this.bulletParent || !this.bulletParent.isValid) {
            if (this.bulletParentPath.length > 0) {
              this.bulletParent = find(this.bulletParentPath);

              if (!this.bulletParent) {
                console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);
                this.bulletParent = emitter.node;
              }
            }
          }
        }

        static onDestroyEmitter(emitter) {
          this.allEmitters = this.allEmitters.filter(e => e !== emitter);
        }

        static onCreateBullet(bullet) {
          for (let i = 0; i < this.allBullets.length; i++) {
            if (this.allBullets[i] === bullet) {
              return;
            }
          }

          bullet.onCreate();
          this.allBullets.push(bullet);
          bullet.node.setParent(this.bulletParent, true);
        }

        static onDestroyBullet(bullet) {
          this.allBullets = this.allBullets.filter(b => b !== bullet);
        }

        static tickEmitters(dt) {
          for (const emitter of this.allEmitters) {
            emitter.tick(dt);
          }
        }

        static tickBullets(dt) {
          for (const bullet of this.allBullets) {
            bullet.tick(dt);
          }
        }

        static destroyAllBullets() {
          for (const bullet of this.allBullets) {
            bullet.destroySelf();
          }

          this.allBullets = [];
        }

      });

      /**
       * All active bullets
       */
      BulletSystem.allBullets = [];

      /**
       * All active emitters
       */
      BulletSystem.allEmitters = [];
      BulletSystem.bulletParentPath = 'Canvas/GameUI/bullet_root';
      // public static isEmitterEnabled: boolean = true;
      // public static isBulletEnabled: boolean = true;
      BulletSystem.bulletParent = void 0;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=76b779ee29a3d17c21be0f960df232efa5fcb95b.js.map