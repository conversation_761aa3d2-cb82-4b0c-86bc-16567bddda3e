System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, _crd, ccclass, property, eMoveModifier, eEasing;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "f66affuZgJGr7LaYjgZCHpD", "IMovable", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * Base interface for all move-able objects
       */

      _export("eMoveModifier", eMoveModifier = /*#__PURE__*/function (eMoveModifier) {
        eMoveModifier[eMoveModifier["Speed"] = 0] = "Speed";
        eMoveModifier[eMoveModifier["SpeedAngle"] = 1] = "SpeedAngle";
        eMoveModifier[eMoveModifier["Acceleration"] = 2] = "Acceleration";
        eMoveModifier[eMoveModifier["AccelerationAngle"] = 3] = "AccelerationAngle";
        return eMoveModifier;
      }({}));

      _export("eEasing", eEasing = /*#__PURE__*/function (eEasing) {
        eEasing[eEasing["Linear"] = 0] = "Linear";
        eEasing[eEasing["InSine"] = 1] = "InSine";
        eEasing[eEasing["OutSine"] = 2] = "OutSine";
        eEasing[eEasing["InOutSine"] = 3] = "InOutSine";
        eEasing[eEasing["InQuad"] = 4] = "InQuad";
        eEasing[eEasing["OutQuad"] = 5] = "OutQuad";
        eEasing[eEasing["InOutQuad"] = 6] = "InOutQuad";
        return eEasing;
      }({}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=98fe2b964e83430d575daf4a892176f46e0bd6cd.js.map