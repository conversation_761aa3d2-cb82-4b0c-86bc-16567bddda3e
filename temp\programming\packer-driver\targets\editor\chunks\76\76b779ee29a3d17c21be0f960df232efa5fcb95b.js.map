{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/BulletSystem.ts"], "names": ["BulletSystem", "_decorator", "find", "ccclass", "onCreateEmitter", "emitter", "i", "allEmitters", "length", "push", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "bulletParentPath", "console", "warn", "node", "onDestroyEmitter", "filter", "e", "onCreateBullet", "bullet", "allBullets", "onCreate", "setParent", "onDestroyBullet", "b", "tickEmitters", "dt", "tick", "tickBullets", "destroyAllBullets", "destroySelf"], "mappings": ";;;iGASaA,Y;;;;;;;;;;;;;;;;;;;AATJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;;;;;;;;OAGf;AAAEC,QAAAA;AAAF,O,GAAcF,U;AAEpB;AACA;AACA;AACA;;8BACaD,Y,GAAN,MAAMA,YAAN,CAAmB;AAiBO,eAAfI,eAAe,CAACC,OAAD,EAAkB;AAC3C,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,WAAL,CAAiBC,MAArC,EAA6CF,CAAC,EAA9C,EAAkD;AAC9C,gBAAI,KAAKC,WAAL,CAAiBD,CAAjB,MAAwBD,OAA5B,EAAqC;AACjC;AACH;AACJ;;AAED,eAAKE,WAAL,CAAiBE,IAAjB,CAAsBJ,OAAtB;;AAEA,cAAI,CAAC,KAAKK,YAAN,IAAsB,CAAC,KAAKA,YAAL,CAAkBC,OAA7C,EAAsD;AAClD,gBAAI,KAAKC,gBAAL,CAAsBJ,MAAtB,GAA+B,CAAnC,EAAsC;AAClC,mBAAKE,YAAL,GAAoBR,IAAI,CAAC,KAAKU,gBAAN,CAAxB;;AACA,kBAAI,CAAC,KAAKF,YAAV,EAAwB;AACpBG,gBAAAA,OAAO,CAACC,IAAR,CAAa,oBAAoB,KAAKF,gBAAtC;AACA,qBAAKF,YAAL,GAAoBL,OAAO,CAACU,IAA5B;AACH;AACJ;AACJ;AACJ;;AAE6B,eAAhBC,gBAAgB,CAACX,OAAD,EAAkB;AAC5C,eAAKE,WAAL,GAAmB,KAAKA,WAAL,CAAiBU,MAAjB,CAAwBC,CAAC,IAAIA,CAAC,KAAKb,OAAnC,CAAnB;AACH;;AAE2B,eAAdc,cAAc,CAACC,MAAD,EAAiB;AACzC,eAAK,IAAId,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKe,UAAL,CAAgBb,MAApC,EAA4CF,CAAC,EAA7C,EAAiD;AAC7C,gBAAI,KAAKe,UAAL,CAAgBf,CAAhB,MAAuBc,MAA3B,EAAmC;AAC/B;AACH;AACJ;;AAEDA,UAAAA,MAAM,CAACE,QAAP;AACA,eAAKD,UAAL,CAAgBZ,IAAhB,CAAqBW,MAArB;AACAA,UAAAA,MAAM,CAACL,IAAP,CAAYQ,SAAZ,CAAsB,KAAKb,YAA3B,EAAyC,IAAzC;AACH;;AAE4B,eAAfc,eAAe,CAACJ,MAAD,EAAiB;AAC1C,eAAKC,UAAL,GAAkB,KAAKA,UAAL,CAAgBJ,MAAhB,CAAuBQ,CAAC,IAAIA,CAAC,KAAKL,MAAlC,CAAlB;AACH;;AAEyB,eAAZM,YAAY,CAACC,EAAD,EAAY;AAClC,eAAK,MAAMtB,OAAX,IAAsB,KAAKE,WAA3B,EAAwC;AACpCF,YAAAA,OAAO,CAACuB,IAAR,CAAaD,EAAb;AACH;AACJ;;AAEwB,eAAXE,WAAW,CAACF,EAAD,EAAY;AACjC,eAAK,MAAMP,MAAX,IAAqB,KAAKC,UAA1B,EAAsC;AAClCD,YAAAA,MAAM,CAACQ,IAAP,CAAYD,EAAZ;AACH;AACJ;;AAE8B,eAAjBG,iBAAiB,GAAG;AAC9B,eAAK,MAAMV,MAAX,IAAqB,KAAKC,UAA1B,EAAsC;AAClCD,YAAAA,MAAM,CAACW,WAAP;AACH;;AACD,eAAKV,UAAL,GAAkB,EAAlB;AACH;;AA1EqB,O;;AAEtB;AACJ;AACA;AAJarB,MAAAA,Y,CAKKqB,U,GAAuB,E;;AAErC;AACJ;AACA;AATarB,MAAAA,Y,CAUKO,W,GAAyB,E;AAV9BP,MAAAA,Y,CAYKY,gB,GAA2B,2B;AACzC;AACA;AAdSZ,MAAAA,Y,CAeKU,Y", "sourcesContent": ["import { _decorator, find, Vec3, Node } from \"cc\";\nimport { Bullet } from \"./Bullet\";\nimport { Emitter } from \"./Emitter\";\nconst { ccclass } = _decorator;\n\n/**\n * BulletSystem - manages all bullets in the game world\n * Handles bullet creation, movement, collision, and cleanup\n */\nexport class BulletSystem {\n\n    /**\n     * All active bullets\n     */\n    public static allBullets: Bullet[] = [];\n\n    /**\n     * All active emitters\n     */\n    public static allEmitters: Emitter[] = [];\n\n    public static bulletParentPath: string = 'Canvas/GameUI/bullet_root';\n    // public static isEmitterEnabled: boolean = true;\n    // public static isBulletEnabled: boolean = true;\n    public static bulletParent: Node;\n\n    public static onCreateEmitter(emitter:Emitter) {\n        for (let i = 0; i < this.allEmitters.length; i++) {\n            if (this.allEmitters[i] === emitter) {\n                return;\n            }\n        }\n\n        this.allEmitters.push(emitter);\n\n        if (!this.bulletParent || !this.bulletParent.isValid) {\n            if (this.bulletParentPath.length > 0) {\n                this.bulletParent = find(this.bulletParentPath);\n                if (!this.bulletParent) {\n                    console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);\n                    this.bulletParent = emitter.node;\n                }\n            }\n        }\n    }\n\n    public static onDestroyEmitter(emitter:Emitter) {\n        this.allEmitters = this.allEmitters.filter(e => e !== emitter);\n    }\n\n    public static onCreateBullet(bullet: Bullet) {\n        for (let i = 0; i < this.allBullets.length; i++) {\n            if (this.allBullets[i] === bullet) {\n                return;\n            }\n        }\n\n        bullet.onCreate();\n        this.allBullets.push(bullet);\n        bullet.node.setParent(this.bulletParent, true);\n    }\n\n    public static onDestroyBullet(bullet: Bullet) {\n        this.allBullets = this.allBullets.filter(b => b !== bullet);\n    }\n\n    public static tickEmitters(dt:number) {\n        for (const emitter of this.allEmitters) {\n            emitter.tick(dt);\n        }\n    }\n\n    public static tickBullets(dt:number) {\n        for (const bullet of this.allBullets) {\n            bullet.tick(dt);\n        }\n    }\n\n    public static destroyAllBullets() {\n        for (const bullet of this.allBullets) {\n            bullet.destroySelf();\n        }\n        this.allBullets = [];\n    }\n}\n"]}