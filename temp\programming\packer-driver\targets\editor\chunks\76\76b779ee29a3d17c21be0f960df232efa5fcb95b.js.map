{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/BulletSystem.ts"], "names": ["BulletSystem", "_decorator", "ccclass"], "mappings": ";;;8EAOaA,Y;;;;;;;;;AAPJC,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcD,U;AAEpB;AACA;AACA;AACA;;8BACaD,Y,GAAN,MAAMA,YAAN,CAAmB,E", "sourcesContent": ["import { _decorator, Vec3, Node } from \"cc\";\nconst { ccclass } = _decorator;\n\n/**\n * BulletSystem - manages all bullets in the game world\n * Handles bullet creation, movement, collision, and cleanup\n */\nexport class BulletSystem {\n    \n}\n"]}