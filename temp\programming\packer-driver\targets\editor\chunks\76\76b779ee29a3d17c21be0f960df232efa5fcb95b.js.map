{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/BulletSystem.ts"], "names": ["BulletSystem", "_decorator", "find", "ccclass", "onCreateEmitter", "emitter", "i", "allEmitters", "length", "push", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "bulletParentPath", "console", "warn", "node", "onDestroyEmitter", "filter", "e", "onCreateBullet", "bullet", "allBullets", "<PERSON><PERSON><PERSON><PERSON>", "onDestroyBullet", "b", "tickEmitters", "dt", "tick", "tickBullets", "destroyAllBullets", "destroySelf"], "mappings": ";;;iGASaA,Y;;;;;;;;;;;;;;;;;;;AATJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;;;;;;;;OAGf;AAAEC,QAAAA;AAAF,O,GAAcF,U;AAEpB;AACA;AACA;AACA;;8BACaD,Y,GAAN,MAAMA,YAAN,CAAmB;AAiBO,eAAfI,eAAe,CAACC,OAAD,EAAkB;AAC3C,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,WAAL,CAAiBC,MAArC,EAA6CF,CAAC,EAA9C,EAAkD;AAC9C,gBAAI,KAAKC,WAAL,CAAiBD,CAAjB,MAAwBD,OAA5B,EAAqC;AACjC;AACH;AACJ;;AAED,eAAKE,WAAL,CAAiBE,IAAjB,CAAsBJ,OAAtB;;AAEA,cAAI,CAAC,KAAKK,YAAN,IAAsB,CAAC,KAAKA,YAAL,CAAkBC,OAA7C,EAAsD;AAClD,gBAAI,KAAKC,gBAAL,CAAsBJ,MAAtB,GAA+B,CAAnC,EAAsC;AAClC,mBAAKE,YAAL,GAAoBR,IAAI,CAAC,KAAKU,gBAAN,CAAxB;;AACA,kBAAI,CAAC,KAAKF,YAAV,EAAwB;AACpBG,gBAAAA,OAAO,CAACC,IAAR,CAAa,oBAAoB,KAAKF,gBAAtC;AACA,qBAAKF,YAAL,GAAoBL,OAAO,CAACU,IAA5B;AACH;AACJ;AACJ;AACJ;;AAE6B,eAAhBC,gBAAgB,CAACX,OAAD,EAAkB;AAC5C,eAAKE,WAAL,GAAmB,KAAKA,WAAL,CAAiBU,MAAjB,CAAwBC,CAAC,IAAIA,CAAC,KAAKb,OAAnC,CAAnB;AACH;;AAE2B,eAAdc,cAAc,CAACC,MAAD,EAAiB;AACzC,eAAKC,UAAL,CAAgBZ,IAAhB,CAAqBW,MAArB;AACApB,UAAAA,YAAY,CAACU,YAAb,CAA0BY,QAA1B,CAAmCF,MAAM,CAACL,IAA1C;AACH;;AAE4B,eAAfQ,eAAe,CAACH,MAAD,EAAiB;AAC1C,eAAKC,UAAL,GAAkB,KAAKA,UAAL,CAAgBJ,MAAhB,CAAuBO,CAAC,IAAIA,CAAC,KAAKJ,MAAlC,CAAlB;AACH;;AAEyB,eAAZK,YAAY,CAACC,EAAD,EAAY;AAClC,eAAK,MAAMrB,OAAX,IAAsB,KAAKE,WAA3B,EAAwC;AACpCF,YAAAA,OAAO,CAACsB,IAAR,CAAaD,EAAb;AACH;AACJ;;AAEwB,eAAXE,WAAW,CAACF,EAAD,EAAY;AACjC,eAAK,MAAMN,MAAX,IAAqB,KAAKC,UAA1B,EAAsC;AAClCD,YAAAA,MAAM,CAACO,IAAP,CAAYD,EAAZ;AACH;AACJ;;AAE8B,eAAjBG,iBAAiB,GAAG;AAC9B,eAAK,MAAMT,MAAX,IAAqB,KAAKC,UAA1B,EAAsC;AAClCD,YAAAA,MAAM,CAACU,WAAP;AACH;;AACD,eAAKT,UAAL,GAAkB,EAAlB;AACH;;AAnEqB,O;;AAEtB;AACJ;AACA;AAJarB,MAAAA,Y,CAKKqB,U,GAAuB,E;;AAErC;AACJ;AACA;AATarB,MAAAA,Y,CAUKO,W,GAAyB,E;AAV9BP,MAAAA,Y,CAYKY,gB,GAA2B,2B;AACzC;AACA;AAdSZ,MAAAA,Y,CAeKU,Y", "sourcesContent": ["import { _decorator, find, Vec3, Node } from \"cc\";\nimport { Bullet } from \"./Bullet\";\nimport { Emitter } from \"./Emitter\";\nconst { ccclass } = _decorator;\n\n/**\n * BulletSystem - manages all bullets in the game world\n * Handles bullet creation, movement, collision, and cleanup\n */\nexport class BulletSystem {\n\n    /**\n     * All active bullets\n     */\n    public static allBullets: Bullet[] = [];\n\n    /**\n     * All active emitters\n     */\n    public static allEmitters: Emitter[] = [];\n\n    public static bulletParentPath: string = 'Canvas/GameUI/bullet_root';\n    // public static isEmitterEnabled: boolean = true;\n    // public static isBulletEnabled: boolean = true;\n    public static bulletParent: Node;\n\n    public static onCreateEmitter(emitter:Emitter) {\n        for (let i = 0; i < this.allEmitters.length; i++) {\n            if (this.allEmitters[i] === emitter) {\n                return;\n            }\n        }\n\n        this.allEmitters.push(emitter);\n\n        if (!this.bulletParent || !this.bulletParent.isValid) {\n            if (this.bulletParentPath.length > 0) {\n                this.bulletParent = find(this.bulletParentPath);\n                if (!this.bulletParent) {\n                    console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);\n                    this.bulletParent = emitter.node;\n                }\n            }\n        }\n    }\n\n    public static onDestroyEmitter(emitter:Emitter) {\n        this.allEmitters = this.allEmitters.filter(e => e !== emitter);\n    }\n\n    public static onCreateBullet(bullet: Bullet) {\n        this.allBullets.push(bullet);\n        BulletSystem.bulletParent.addChild(bullet.node);\n    }\n\n    public static onDestroyBullet(bullet: Bullet) {\n        this.allBullets = this.allBullets.filter(b => b !== bullet);\n    }\n\n    public static tickEmitters(dt:number) {\n        for (const emitter of this.allEmitters) {\n            emitter.tick(dt);\n        }\n    }\n\n    public static tickBullets(dt:number) {\n        for (const bullet of this.allBullets) {\n            bullet.tick(dt);\n        }\n    }\n\n    public static destroyAllBullets() {\n        for (const bullet of this.allBullets) {\n            bullet.destroySelf();\n        }\n        this.allBullets = [];\n    }\n}\n"]}