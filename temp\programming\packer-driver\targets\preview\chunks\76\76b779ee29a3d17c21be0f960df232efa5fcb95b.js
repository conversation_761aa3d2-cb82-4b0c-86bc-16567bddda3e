System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BulletSystem, _crd, ccclass;

  _export("BulletSystem", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7cb83oXVZVMBJYZY7lJNPxV", "BulletSystem", undefined);

      __checkObsolete__(['_decorator', 'Vec3', 'Node']);

      ({
        ccclass
      } = _decorator);
      /**
       * BulletSystem - manages all bullets in the game world
       * Handles bullet creation, movement, collision, and cleanup
       */

      _export("BulletSystem", BulletSystem = class BulletSystem {});

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=76b779ee29a3d17c21be0f960df232efa5fcb95b.js.map