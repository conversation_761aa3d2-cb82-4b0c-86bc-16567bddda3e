System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Prefab, Component, EDITOR, Bullet, EmitterData, ObjectPool, BulletSystem, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _class3, _crd, ccclass, executeInEditMode, property, playOnFocus, eEmitterStatus, Emitter;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterData(extras) {
    _reporterNs.report("EmitterData", "../../../Game/data/EmitterData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfObjectPool(extras) {
    _reporterNs.report("ObjectPool", "./ObjectPool", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Prefab = _cc.Prefab;
      Component = _cc.Component;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      Bullet = _unresolved_2.Bullet;
    }, function (_unresolved_3) {
      EmitterData = _unresolved_3.EmitterData;
    }, function (_unresolved_4) {
      ObjectPool = _unresolved_4.ObjectPool;
    }, function (_unresolved_5) {
      BulletSystem = _unresolved_5.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2564dArcRFKZKoo3odCQrHw", "Emitter", undefined);

      __checkObsolete__(['_decorator', 'instantiate', 'Node', 'Prefab', 'Component']);

      ({
        ccclass,
        executeInEditMode,
        property,
        playOnFocus
      } = _decorator);

      _export("eEmitterStatus", eEmitterStatus = /*#__PURE__*/function (eEmitterStatus) {
        eEmitterStatus[eEmitterStatus["None"] = 0] = "None";
        eEmitterStatus[eEmitterStatus["Prewarm"] = 1] = "Prewarm";
        eEmitterStatus[eEmitterStatus["Emitting"] = 2] = "Emitting";
        eEmitterStatus[eEmitterStatus["LoopEndReached"] = 3] = "LoopEndReached";
        eEmitterStatus[eEmitterStatus["Completed"] = 4] = "Completed";
        return eEmitterStatus;
      }({}));

      _export("Emitter", Emitter = (_dec = ccclass('Emitter'), _dec2 = property({
        type: Prefab,
        displayName: "Bullet Prefab"
      }), _dec3 = property({
        type: _crd && EmitterData === void 0 ? (_reportPossibleCrUseOfEmitterData({
          error: Error()
        }), EmitterData) : EmitterData,
        displayName: "Emitter Data"
      }), _dec(_class = executeInEditMode(_class = playOnFocus(_class = (_class2 = (_class3 = class Emitter extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "bulletPrefab", _descriptor, this);

          _initializerDefineProperty(this, "emitterData", _descriptor2, this);

          this.updateInEditor = false;
          this.status = eEmitterStatus.None;
          this.statusElapsedTime = 0;
          this.totalElapsedTime = 0;
          this.isEmitting = false;
          this.nextEmitTime = 0;
        }

        // 是否在编辑器中更新
        start() {
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onCreateEmitter(this);
        }

        update(dt) {
          if (EDITOR && this.updateInEditor) {
            this.tick(dt);
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).tickBullets(dt);
          }
        }

        resetInEditor() {
          this.updateInEditor = true;
        }

        onFocusInEditor() {
          this.updateInEditor = true;
        }

        onLostFocusInEditor() {
          this.updateInEditor = false;

          if ((_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent && (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent.isValid) {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).destroyAllBullets();
          }
        }

        /**
         * public apis
         */
        changeStatus(status) {
          this.status = status;
          this.statusElapsedTime = 0;
        }

        scheduleNextEmit() {
          // Schedule the next emit after emitInterval
          this.nextEmitTime = this.statusElapsedTime + this.emitterData.emitInterval;
        }

        startEmitting() {
          this.isEmitting = true; // 下一次update时触发发射
          // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射
        }

        stopEmitting() {
          this.isEmitting = false; // Clear any scheduled per-emit bullets

          this.unscheduleAllCallbacks();
        }

        canEmit() {
          // 检查是否可以触发发射
          // Override this method in subclasses to add custom trigger conditions
          return true;
        }

        emit() {
          var _this = this;

          var _loop = function _loop() {
            var localIndex = i;

            if (_this.emitterData.perEmitInterval > 0) {
              _this.scheduleOnce(() => {
                _this.emitSingle(localIndex); // Logic to emit the bullet after the interval

              }, _this.emitterData.perEmitInterval * localIndex);
            }
          };

          for (var i = 0; i < this.emitterData.perEmitCount; i++) {
            _loop();
          }
        }

        tryEmit() {
          if (this.canEmit()) {
            this.emit();
            return true;
          }

          return false;
        }

        emitSingle(index) {
          var direction = this.getSpawnDirection(index);
          var position = this.getSpawnPosition(index);
          this.createBullet(direction, position);
        }
        /**
         * Calculate the direction for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Direction vector {x, y}
         */


        getSpawnDirection(index) {
          // 计算发射方向
          var angleOffset = this.emitterData.count > 1 ? this.emitterData.arc / (this.emitterData.count - 1) * index - this.emitterData.arc / 2 : 0;
          var radian = (this.emitterData.angle + angleOffset) * (Math.PI / 180);
          return {
            x: Math.cos(radian),
            y: Math.sin(radian)
          };
        }
        /**
         * Get the spawn position for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Position offset from emitter center
         */


        getSpawnPosition(index) {
          if (this.emitterData.radius <= 0) {
            return {
              x: 0,
              y: 0
            };
          }

          var direction = this.getSpawnDirection(index);
          return {
            x: direction.x * this.emitterData.radius,
            y: direction.y * this.emitterData.radius
          };
        }

        createBullet(direction, position) {
          if (!this.bulletPrefab) {
            console.warn("EmitterArc: No bullet prefab assigned");
            return null;
          }

          var bulletNode = (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
            error: Error()
          }), ObjectPool) : ObjectPool).getNode((_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent, this.bulletPrefab);

          if (!bulletNode) {
            console.error("EmitterArc: Failed to instantiate bullet prefab");
            return null;
          } // Get the bullet component


          var bullet = bulletNode.getComponent(_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);

          if (!bullet) {
            console.error("Emitter: Bullet prefab does not have Bullet component");
            bulletNode.destroy();
            return null;
          }

          if (EDITOR) {
            bulletNode.name = Emitter.kBulletNameInEditor;
          } // Set bullet position relative to emitter


          var emitterPos = this.node.getWorldPosition();
          bulletNode.setWorldPosition(emitterPos.x + position.x, emitterPos.y + position.y, emitterPos.z); // Set bullet rotation from direction

          bulletNode.setRotationFromEuler(0, 0, Math.atan2(direction.y, direction.x) * (180 / Math.PI));
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onCreateBullet(bullet);
          return bulletNode;
        } // Implementation of CObject abstract methods


        onObjectInit() {// Override in subclasses if needed
        }

        onObjectDestroy() {// Clean up any scheduled callbacks
        }
        /**
         * Return true if this.node is in screen
         */


        isInScreen() {
          // TODO: Get mainCamera.containsNode(this.node)
          return true;
        }

        tick(deltaTime) {
          if (!this.emitterData) {
            return;
          }

          this.statusElapsedTime += deltaTime;
          this.totalElapsedTime += deltaTime;

          switch (this.status) {
            case eEmitterStatus.None:
              this.updateStatusNone();
              break;

            case eEmitterStatus.Prewarm:
              this.updateStatusPrewarm();
              break;

            case eEmitterStatus.Emitting:
              this.updateStatusEmitting(deltaTime);
              break;

            case eEmitterStatus.LoopEndReached:
              this.updateStatusLoopEndReached();
              break;

            case eEmitterStatus.Completed:
              this.updateStatusCompleted();
              break;

            default:
              break;
          }
        }

        updateStatusNone() {
          if (this.statusElapsedTime >= this.emitterData.initialDelay) {
            this.changeStatus(eEmitterStatus.Prewarm);
          }
        }

        updateStatusPrewarm() {
          if (!this.emitterData.isPreWarm) this.changeStatus(eEmitterStatus.Emitting);else {
            if (this.statusElapsedTime >= this.emitterData.preWarmDuration) {
              this.changeStatus(eEmitterStatus.Emitting);
            }
          }
        }

        updateStatusEmitting(deltaTime) {
          if (this.statusElapsedTime > this.emitterData.emitDuration) {
            this.stopEmitting();
            if (this.emitterData.isLoop) this.changeStatus(eEmitterStatus.LoopEndReached);else this.changeStatus(eEmitterStatus.Completed);
            return;
          } // Start emitting if not already started


          if (!this.isEmitting) {
            this.startEmitting();
          } else if (this.isEmitting && this.statusElapsedTime >= this.nextEmitTime) {
            // Check if it's time for the next emit
            this.tryEmit();
            this.scheduleNextEmit();
          }
        }

        updateStatusLoopEndReached() {
          if (this.statusElapsedTime >= this.emitterData.loopInterval) {
            this.changeStatus(eEmitterStatus.Emitting);
          }
        }

        updateStatusCompleted() {// Do nothing or cleanup if needed
        }

      }, _class3.kBulletNameInEditor = "_bullet_", _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bulletPrefab", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "emitterData", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3633188a41785f60165bff4819b6418349d126c8.js.map