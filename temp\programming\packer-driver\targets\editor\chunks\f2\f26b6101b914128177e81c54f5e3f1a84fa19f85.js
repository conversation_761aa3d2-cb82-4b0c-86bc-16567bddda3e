System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, BulletData, ObjectPool, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, Bullet;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBulletData(extras) {
    _reporterNs.report("BulletData", "../../../Game/data/BulletData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfObjectPool(extras) {
    _reporterNs.report("ObjectPool", "./ObjectPool", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      BulletData = _unresolved_2.BulletData;
    }, function (_unresolved_3) {
      ObjectPool = _unresolved_3.ObjectPool;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "21904rFEfNCRbgYx1LE+M6P", "Bullet", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node']);

      ({
        ccclass,
        property
      } = _decorator); // 子弹 Bullet 伤害计算 
      // Weapon -> 发射器, 喷火, 技能武器, 激光
      // WeaponSlot -> SetWeapon

      _export("Bullet", Bullet = (_dec = ccclass('Bullet'), _dec2 = property({
        type: _crd && BulletData === void 0 ? (_reportPossibleCrUseOfBulletData({
          error: Error()
        }), BulletData) : BulletData
      }), _dec(_class = (_class2 = class Bullet extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "bulletData", _descriptor, this);

          this.isRunning = void 0;
        }

        /**
         * 
         */
        onCollisionEnter(other, self) {// 判断另一个node也是子弹或者非子弹, 进行相应处理
        }

        destroySelf() {
          this.isRunning = false;

          const cb = () => {
            if (CC_EDITOR) {
              this.node.destroy();
            } else {
              (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
                error: Error()
              }), ObjectPool) : ObjectPool).returnNode(this.node);
            }
          };

          if (this.bulletData.delayDestroy > 0) {
            this.scheduleOnce(() => {
              cb();
            }, this.bulletData.delayDestroy);
          } else {
            cb();
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bulletData", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f26b6101b914128177e81c54f5e3f1a84fa19f85.js.map