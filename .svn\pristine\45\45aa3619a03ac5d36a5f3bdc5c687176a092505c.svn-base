import { _decorator, Component, Node } from 'cc';
import { IMovable, IMoveModifier, eMoveModifier, eEasing } from './IMovable';
const { ccclass, property } = _decorator;

@ccclass('DefaultMoveModifier')
export class DefaultMoveModifier implements IMoveModifier {

    public targetType: eMoveModifier = eMoveModifier.Speed;
    public targetValue: number = 1.0
    public duration: number = 1.0;
    public easing: eEasing = eEasing.Linear;

    private startValue: number = 0;
    private currentTime: number = 0;
    private isInitialized: boolean = false;

    constructor(targetType: eMoveModifier, targetValue: number, duration: number, easing: eEasing = eEasing.Linear) {
        this.targetType = targetType;
        this.targetValue = targetValue;
        this.duration = duration;
        this.easing = easing;
    }

    isFinished(): boolean {
        return this.currentTime >= this.duration;
    }

    initialize(movable: IMovable) {
        switch (this.targetType) {
            case eMoveModifier.Speed:
                this.startValue = movable.speed;
                break;
            case eMoveModifier.SpeedAngle:
                this.startValue = movable.speedAngle;
                break;
            case eMoveModifier.Acceleration:
                this.startValue = movable.acceleration;
                break;
            case eMoveModifier.AccelerationAngle:
                this.startValue = movable.accelerationAngle;
                break;
        }
        this.isInitialized = true;
    }

    tick(movable: IMovable, deltaTime: number): void {
        // Initialize start value on first tick
        if (!this.isInitialized) {
            this.initialize(movable);
        }

        // Update the appropriate property based on targetType
        switch (this.targetType) {
            case eMoveModifier.Speed:
                movable.speed = this.tickMoveProperty(movable.speed, deltaTime);
                break;
            case eMoveModifier.SpeedAngle:
                movable.speedAngle = this.tickMoveProperty(movable.speedAngle, deltaTime);
                break;
            case eMoveModifier.Acceleration:
                movable.acceleration = this.tickMoveProperty(movable.acceleration, deltaTime);
                break;
            case eMoveModifier.AccelerationAngle:
                movable.accelerationAngle = this.tickMoveProperty(movable.accelerationAngle, deltaTime);
                break;
        }
    }

    tickMoveProperty(currentValue: number, deltaTime: number): number {
        // Update current time
        this.currentTime += deltaTime;

        // Calculate progress ratio (0 to 1)
        let progress = Math.min(1.0, this.currentTime / this.duration);

        // Apply easing function
        let easedProgress = DefaultMoveModifier.applyEasing(this.easing, progress);

        // Interpolate between start value and target value
        return this.startValue + (this.targetValue - this.startValue) * easedProgress;
    }

    static applyEasing(easing: eEasing, t: number): number {
        switch (easing) {
            case eEasing.Linear:
                return t;

            case eEasing.InSine:
                return 1 - Math.cos(t * Math.PI / 2);

            case eEasing.OutSine:
                return Math.sin(t * Math.PI / 2);

            case eEasing.InOutSine:
                return -(Math.cos(Math.PI * t) - 1) / 2;

            case eEasing.InQuad:
                return t * t;

            case eEasing.OutQuad:
                return 1 - (1 - t) * (1 - t);

            case eEasing.InOutQuad:
                return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;

            default:
                return t;
        }
    }
}


