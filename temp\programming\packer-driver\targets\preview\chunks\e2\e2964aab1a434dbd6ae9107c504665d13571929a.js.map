{"version": 3, "sources": ["data:text/javascript,%0A%2F%2F%20This%20module%20is%20auto-generated%20to%20report%20error%20emitted%20when%20try%20to%20load%20module%20file%3A%2F%2F%2FE%3A%2FM2Game%2FClient%2Fassets%2Fscripts%2FGameOld%2Fworld%2Fweapon%2FEmitterArc.ts%20at%20runtime.%0Athrow%20new%20Error(%60Error%3A%20Error%20when%20fetching%20file%20E%3A%5CM2Game%5CClient%5Cassets%5Cscripts%5CGameOld%5Cworld%5Cweapon%5CEmitterArc.ts%3A%20Error%3A%20ENOENT%3A%20no%20such%20file%20or%20directory%2C%20open%20'E%3A%5CM2Game%5CClient%5Cassets%5Cscripts%5CGameOld%5Cworld%5Cweapon%5CEmitterArc.ts'%60)%3B%0A%20%20%20%20%20%20%20%20"], "names": ["Error"], "mappings": ";;;;;;AACA;AACA,YAAM,IAAIA,KAAJ,2MAAN", "sourcesContent": ["\n// This module is auto-generated to report error emitted when try to load module file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/EmitterArc.ts at runtime.\nthrow new Error(`Error: Error when fetching file E:\\M2Game\\Client\\assets\\scripts\\GameOld\\world\\weapon\\EmitterArc.ts: Error: ENOENT: no such file or directory, open 'E:\\M2Game\\Client\\assets\\scripts\\GameOld\\world\\weapon\\EmitterArc.ts'`);\n        "]}