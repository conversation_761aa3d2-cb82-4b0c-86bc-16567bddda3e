{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/BulletData.ts"], "names": ["_decorator", "ccclass", "property", "BulletData", "displayName"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U;AAE9B;AACA;AACA;AACA;;4BAEaG,U,WADZF,OAAO,CAAC,YAAD,C,UAEHC,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAKRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAGRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,2BAtBb,MACaD,UADb,CACwB;AAAA;AAAA;;AAEsB;AAFtB;;AAIsB;AAJtB;;AAMsB;AAE1C;AACA;AAToB;;AAWsB;AAXtB;;AAasB;AAbtB;;AAesB;AAftB;;AAiBsB;AAjBtB;;AAmBsB;AAnBtB;AAAA,UAsBuB;;;AAtBvB,O;;;;;iBAEQ,K;;;;;;;iBAEF,K;;;;;;;iBAEK,K;;;;;;;iBAKX,E;;;;;;;iBAEF,C;;;;;;;iBAED,C;;;;;;;iBAEK,C;;;;;;;iBAED,C;;;;;;;iBAGG,C", "sourcesContent": ["import { _decorator, error, v2, Vec2 } from \"cc\";\r\nimport { Tools } from \"../utils/Tools\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 子弹数据\r\n * 所有时间相关的，单位都是秒(s)\r\n */\r\n@ccclass(\"BulletData\")\r\nexport class BulletData {\r\n    @property({displayName: '是否朝向行进方向'})\r\n    isFacingMoveDir : boolean = false;        // 是否朝向行进方向\r\n    @property({displayName: '是否可被破坏'})\r\n    isDestructive : boolean = false;          // 是否可被破坏\r\n    @property({displayName: '命中时是否被销毁'})\r\n    isDestructiveOnHit : boolean = false;     // 命中时是否被销毁\r\n\r\n    // @property({displayName: '子弹Prefab'})\r\n    // prefab : string;                          // 子弹Prefab: 考虑包含拖尾特效、颜色、缩放等\r\n    @property({displayName: '子弹持续时间'})\r\n    duration : number = 10;                   // 子弹持续时间(超出后销毁回收)\r\n    @property({displayName: '子弹伤害'})\r\n    damage : number = 1;                      // 子弹伤害\r\n    @property({displayName: '子弹速度'})\r\n    speed : number = 1;                       // 子弹速度\r\n    @property({displayName: '子弹加速度'})\r\n    accelerate : number = 0;                  // 子弹加速度\r\n    @property({displayName: '加速度方向'})\r\n    acceAngle : number = 0;                   // 加速度方向(角度)\r\n\r\n    @property({displayName: '延迟销毁时间'})\r\n    delayDestroy : number = 0;                 // 延迟销毁时间\r\n}\r\n"]}