{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Emitter.ts"], "names": ["_decorator", "misc", "Prefab", "Component", "EDITOR", "Bullet", "EmitterData", "ObjectPool", "BulletSystem", "ccclass", "executeInEditMode", "property", "playOnFocus", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "Emitter", "type", "displayName", "updateInEditor", "status", "None", "statusElapsedTime", "totalElapsedTime", "isEmitting", "nextEmitTime", "start", "onCreateEmitter", "update", "dt", "tick", "tickBullets", "resetInEditor", "onFocusInEditor", "onLostFocusInEditor", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "destroyAllBullets", "changeStatus", "scheduleNextEmit", "emitterData", "emitInterval", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "perEmitInterval", "i", "count", "localIndex", "localPerEmitIndex", "j", "scheduleOnce", "emitSingle", "perEmitCount", "tryEmit", "index", "perEmitIndex", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "arc", "radian", "angle", "x", "Math", "cos", "y", "sin", "perEmitOffsetX", "radius", "bulletPrefab", "console", "warn", "bulletNode", "getNode", "error", "bullet", "getComponent", "destroy", "name", "kBulletNameInEditor", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "onCreateBullet", "mover", "speedAngle", "atan2", "speed", "emitPower", "onObjectInit", "onObjectDestroy", "isInScreen", "deltaTime", "updateStatusNone", "Prewarm", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted", "initialDelay", "isPreWarm", "preWarmDuration", "emitDuration", "isLoop", "loopInterval"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAyBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AAC7CC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA;AAAxC,O,GAAwDZ,U;OACxD;AAAEa,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCb,I;;gCAEnCc,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;yBAOCC,O,WAHZP,OAAO,CAAC,SAAD,C,UAOHE,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEf,MAAP;AAAegB,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAGRP,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA,sCAAL;AAAoBC,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,gBATZR,iB,UACAE,W,gCAFD,MAGaI,OAHb,SAG6Bb,SAH7B,CAGuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAU5BgB,cAV4B,GAUD,KAVC;AAAA,eAuCzBC,MAvCyB,GAuCAL,cAAc,CAACM,IAvCf;AAAA,eAwCzBC,iBAxCyB,GAwCG,CAxCH;AAAA,eAyCzBC,gBAzCyB,GAyCE,CAzCF;AAAA,eA0CzBC,UA1CyB,GA0CH,KA1CG;AAAA,eA2CzBC,YA3CyB,GA2CF,CA3CE;AAAA;;AAUM;AAE/BC,QAAAA,KAAK,GAAU;AACrB;AAAA;AAAA,4CAAaC,eAAb,CAA6B,IAA7B;AACH;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAoB;AAChC,cAAIzB,MAAM,IAAI,KAAKe,cAAnB,EAAmC;AAC/B,iBAAKW,IAAL,CAAUD,EAAV;AACA;AAAA;AAAA,8CAAaE,WAAb,CAAyBF,EAAzB;AACH;AACJ;;AAEMG,QAAAA,aAAa,GAAG;AACnB,eAAKb,cAAL,GAAsB,IAAtB;AACH;;AAEMc,QAAAA,eAAe,GAAG;AACrB,eAAKd,cAAL,GAAsB,IAAtB;AACH;;AAEMe,QAAAA,mBAAmB,GAAG;AACzB,eAAKf,cAAL,GAAsB,KAAtB;;AAEA,cAAI;AAAA;AAAA,4CAAagB,YAAb,IAA6B;AAAA;AAAA,4CAAaA,YAAb,CAA0BC,OAA3D,EAAoE;AAChE;AAAA;AAAA,8CAAaC,iBAAb;AACH;AACJ;;AAQD;AACJ;AACA;AACIC,QAAAA,YAAY,CAAClB,MAAD,EAAyB;AACjC,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKE,iBAAL,GAAyB,CAAzB;AACH;;AAESiB,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAKd,YAAL,GAAoB,KAAKH,iBAAL,GAAyB,KAAKkB,WAAL,CAAiBC,YAA9D;AACH;;AAESC,QAAAA,aAAa,GAAG;AACtB,eAAKlB,UAAL,GAAkB,IAAlB,CADsB,CAEtB;AACA;AACH;;AAESmB,QAAAA,YAAY,GAAG;AACrB,eAAKnB,UAAL,GAAkB,KAAlB,CADqB,CAErB;;AACA,eAAKoB,sBAAL;AACH;;AAESC,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AAAA;;AACnB,cAAI,KAAKN,WAAL,CAAiBO,eAAjB,GAAmC,CAAvC,EAA0C;AACtC,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKR,WAAL,CAAiBS,KAArC,EAA4CD,CAAC,EAA7C,EAAiD;AAAA,2CACW;AACpD,oBAAIE,UAAU,GAAGF,CAAjB;AACA,oBAAIG,iBAAiB,GAAGC,CAAxB;;AACA,gBAAA,KAAI,CAACC,YAAL,CAAkB,MAAM;AACpB,kBAAA,KAAI,CAACC,UAAL,CAAgBJ,UAAhB,EAA4BC,iBAA5B,EADoB,CAEpB;;AACH,iBAHD,EAGG,KAAI,CAACX,WAAL,CAAiBO,eAAjB,GAAmCI,iBAHtC;AAIH,eAR4C;;AAC7C,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKZ,WAAL,CAAiBe,YAArC,EAAmDH,CAAC,EAApD;AAAA;AAAA;AAQH;AACJ,WAXD,MAYK;AACD,iBAAK,IAAIJ,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKR,WAAL,CAAiBS,KAArC,EAA4CD,EAAC,EAA7C,EAAiD;AAC7C,mBAAK,IAAII,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKZ,WAAL,CAAiBe,YAArC,EAAmDH,EAAC,EAApD,EAAwD;AACpD,qBAAKE,UAAL,CAAgBN,EAAhB,EAAmBI,EAAnB;AACH;AACJ;AACJ;AACJ;;AAESI,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKX,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESQ,QAAAA,UAAU,CAACG,KAAD,EAAeC,YAAf,EAAqC;AACrD,cAAMC,SAAS,GAAG,KAAKC,iBAAL,CAAuBH,KAAvB,CAAlB;AACA,cAAMI,QAAQ,GAAG,KAAKC,gBAAL,CAAsBL,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKK,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACH,KAAD,EAA0C;AACvD;AACA,cAAMO,WAAW,GAAG,KAAKxB,WAAL,CAAiBS,KAAjB,GAAyB,CAAzB,GAA8B,KAAKT,WAAL,CAAiByB,GAAjB,IAAwB,KAAKzB,WAAL,CAAiBS,KAAjB,GAAyB,CAAjD,CAAD,GAAwDQ,KAAxD,GAAgE,KAAKjB,WAAL,CAAiByB,GAAjB,GAAuB,CAApH,GAAwH,CAA5I;AACA,cAAMC,MAAM,GAAGrD,gBAAgB,CAAC,KAAK2B,WAAL,CAAiB2B,KAAjB,GAAyBH,WAA1B,CAA/B;AACA,iBAAO;AACHI,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASJ,MAAT,CADA;AAEHK,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASN,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIJ,QAAAA,gBAAgB,CAACL,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA,cAAMe,cAAc,GAAI,KAAKjC,WAAL,CAAiBe,YAAjB,GAAgC,CAAhC,GAAqC,KAAKf,WAAL,CAAiBiC,cAAjB,IAAmC,KAAKjC,WAAL,CAAiBe,YAAjB,GAAgC,CAAnE,CAAD,GAA0EG,YAA1E,GAAyF,KAAKlB,WAAL,CAAiBiC,cAAjB,GAAkC,CAA/J,GAAmK,CAA3L;;AACA,cAAI,KAAKjC,WAAL,CAAiBkC,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,mBAAO;AAAEN,cAAAA,CAAC,EAAEK,cAAL;AAAqBF,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,cAAMZ,SAAS,GAAG,KAAKC,iBAAL,CAAuBH,KAAvB,CAAlB;AACA,iBAAO;AACHW,YAAAA,CAAC,EAAET,SAAS,CAACS,CAAV,GAAc,KAAK5B,WAAL,CAAiBkC,MAA/B,GAAwCD,cADxC;AAEHF,YAAAA,CAAC,EAAEZ,SAAS,CAACY,CAAV,GAAc,KAAK/B,WAAL,CAAiBkC;AAF/B,WAAP;AAIH;;AAEDX,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAA0E;AAClF,cAAI,CAAC,KAAKc,YAAV,EAAwB;AACpBC,YAAAA,OAAO,CAACC,IAAR,CAAa,uCAAb;AACA,mBAAO,IAAP;AACH;;AAED,cAAMC,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAa5C,YAAhC,EAA8C,KAAKwC,YAAnD,CAAnB;;AACA,cAAI,CAACG,UAAL,EAAiB;AACbF,YAAAA,OAAO,CAACI,KAAR,CAAc,iDAAd;AACA,mBAAO,IAAP;AACH,WAViF,CAYlF;;;AACA,cAAMC,MAAM,GAAGH,UAAU,CAACI,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAACD,MAAL,EAAa;AACTL,YAAAA,OAAO,CAACI,KAAR,CAAc,uDAAd;AACAF,YAAAA,UAAU,CAACK,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAI/E,MAAJ,EAAY;AACR0E,YAAAA,UAAU,CAACM,IAAX,GAAkBpE,OAAO,CAACqE,mBAA1B;AACH,WAtBiF,CAwBlF;;;AACA,cAAMC,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAV,UAAAA,UAAU,CAACW,gBAAX,CACIH,UAAU,CAAClB,CAAX,GAAeP,QAAQ,CAACO,CAD5B,EAEIkB,UAAU,CAACf,CAAX,GAAeV,QAAQ,CAACU,CAF5B,EAGIe,UAAU,CAACI,CAHf;AAMA;AAAA;AAAA,4CAAaC,cAAb,CAA4BV,MAA5B,EAhCkF,CAkClF;;AACAA,UAAAA,MAAM,CAACW,KAAP,CAAaC,UAAb,GAA0B/E,gBAAgB,CAACuD,IAAI,CAACyB,KAAL,CAAWnC,SAAS,CAACY,CAArB,EAAwBZ,SAAS,CAACS,CAAlC,CAAD,CAA1C;AACAa,UAAAA,MAAM,CAACW,KAAP,CAAaG,KAAb,IAAsB,KAAKvD,WAAL,CAAiBwD,SAAvC,CApCkF,CAqClF;;AAEA,iBAAOlB,UAAP;AACH,SA1LkC,CA4LnC;;;AACUmB,QAAAA,YAAY,GAAS,CAC3B;AACH;;AAESC,QAAAA,eAAe,GAAS,CAC9B;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAEMrE,QAAAA,IAAI,CAACsE,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAK5D,WAAV,EAAuB;AACnB;AACH;;AAED,eAAKlB,iBAAL,IAA0B8E,SAA1B;AACA,eAAK7E,gBAAL,IAAyB6E,SAAzB;;AAEA,kBAAQ,KAAKhF,MAAb;AAEI,iBAAKL,cAAc,CAACM,IAApB;AACI,mBAAKgF,gBAAL;AACA;;AACJ,iBAAKtF,cAAc,CAACuF,OAApB;AACI,mBAAKC,mBAAL;AACA;;AACJ,iBAAKxF,cAAc,CAACyF,QAApB;AACI,mBAAKC,oBAAL,CAA0BL,SAA1B;AACA;;AACJ,iBAAKrF,cAAc,CAAC2F,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAK5F,cAAc,CAAC6F,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;AAoBH;;AAESR,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAK/E,iBAAL,IAA0B,KAAKkB,WAAL,CAAiBsE,YAA/C,EAA6D;AACzD,iBAAKxE,YAAL,CAAkBvB,cAAc,CAACuF,OAAjC;AACH;AACJ;;AAESC,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAK/D,WAAL,CAAiBuE,SAAtB,EACI,KAAKzE,YAAL,CAAkBvB,cAAc,CAACyF,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAKlF,iBAAL,IAA0B,KAAKkB,WAAL,CAAiBwE,eAA/C,EAAgE;AAC5D,mBAAK1E,YAAL,CAAkBvB,cAAc,CAACyF,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,CAACL,SAAD,EAAoB;AAC9C,cAAI,KAAK9E,iBAAL,GAAyB,KAAKkB,WAAL,CAAiByE,YAA9C,EAA4D;AACxD,iBAAKtE,YAAL;AACA,gBAAI,KAAKH,WAAL,CAAiB0E,MAArB,EACI,KAAK5E,YAAL,CAAkBvB,cAAc,CAAC2F,cAAjC,EADJ,KAGI,KAAKpE,YAAL,CAAkBvB,cAAc,CAAC6F,SAAjC;AACJ;AACH,WAR6C,CAU9C;;;AACA,cAAI,CAAC,KAAKpF,UAAV,EAAsB;AAClB,iBAAKkB,aAAL;AACH,WAFD,MAGK,IAAI,KAAKlB,UAAL,IAAmB,KAAKF,iBAAL,IAA0B,KAAKG,YAAtD,EAAoE;AACrE;AACA,iBAAK+B,OAAL;AACA,iBAAKjB,gBAAL;AACH;AACJ;;AAESoE,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKrF,iBAAL,IAA0B,KAAKkB,WAAL,CAAiB2E,YAA/C,EAA6D;AACzD,iBAAK7E,YAAL,CAAkBvB,cAAc,CAACyF,QAAjC;AACH;AACJ;;AAESK,QAAAA,qBAAqB,GAAG,CAC9B;AACH;;AAxRkC,O,UAE5BxB,mB,GAA6B,U;;;;;iBAGb,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, misc, instantiate, Node, Prefab, Component } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { Bullet } from './Bullet';\r\nimport { EmitterData } from '../../../Game/data/EmitterData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { BulletSystem } from './BulletSystem';\r\nconst { ccclass, executeInEditMode, property, playOnFocus } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n@ccclass('Emitter')\r\n@executeInEditMode\r\n@playOnFocus\r\nexport class Emitter extends Component {\r\n\r\n    static kBulletNameInEditor:string = \"_bullet_\";\r\n\r\n    @property({type: Prefab, displayName: \"Bullet Prefab\"})\r\n    bulletPrefab: Prefab = null;\r\n\r\n    @property({type: EmitterData, displayName: \"Emitter Data\"})\r\n    emitterData: EmitterData = null;\r\n\r\n    public updateInEditor : boolean = false; // 是否在编辑器中更新\r\n\r\n    protected start() : void {\r\n        BulletSystem.onCreateEmitter(this);\r\n    }\r\n\r\n    protected update(dt : number): void {\r\n        if (EDITOR && this.updateInEditor) {\r\n            this.tick(dt);\r\n            BulletSystem.tickBullets(dt);\r\n        }\r\n    }\r\n\r\n    public resetInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onFocusInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onLostFocusInEditor() {\r\n        this.updateInEditor = false;\r\n\r\n        if (BulletSystem.bulletParent && BulletSystem.bulletParent.isValid) {\r\n            BulletSystem.destroyAllBullets()\r\n        }\r\n    }\r\n\r\n    protected status: eEmitterStatus = eEmitterStatus.None;\r\n    protected statusElapsedTime: number = 0;\r\n    protected totalElapsedTime: number = 0;\r\n    protected isEmitting: boolean = false;\r\n    protected nextEmitTime: number = 0;\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        this.status = status;\r\n        this.statusElapsedTime = 0;\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // Schedule the next emit after emitInterval\r\n        this.nextEmitTime = this.statusElapsedTime + this.emitterData.emitInterval;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this.isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this.isEmitting = false;\r\n        // Clear any scheduled per-emit bullets\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        if (this.emitterData.perEmitInterval > 0) {\r\n            for (let i = 0; i < this.emitterData.count; i++) {\r\n                for (let j = 0; j < this.emitterData.perEmitCount; j++) {\r\n                    let localIndex = i;\r\n                    let localPerEmitIndex = j;\r\n                    this.scheduleOnce(() => {\r\n                        this.emitSingle(localIndex, localPerEmitIndex);\r\n                        // Logic to emit the bullet after the interval\r\n                    }, this.emitterData.perEmitInterval * localPerEmitIndex);\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            for (let i = 0; i < this.emitterData.count; i++) {\r\n                for (let j = 0; j < this.emitterData.perEmitCount; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number, perEmitIndex: number) {\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.emitterData.count > 1 ? (this.emitterData.arc / (this.emitterData.count - 1)) * index - this.emitterData.arc / 2 : 0;\r\n        const radian = degreesToRadians(this.emitterData.angle + angleOffset);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex\r\n        const perEmitOffsetX = (this.emitterData.perEmitCount > 1 ? (this.emitterData.perEmitOffsetX / (this.emitterData.perEmitCount - 1)) * perEmitIndex - this.emitterData.perEmitOffsetX / 2 : 0);\r\n        if (this.emitterData.radius <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.emitterData.radius + perEmitOffsetX,\r\n            y: direction.y * this.emitterData.radius\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        if (!this.bulletPrefab) {\r\n            console.warn(\"EmitterArc: No bullet prefab assigned\");\r\n            return null;\r\n        }\r\n\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this.bulletPrefab);\r\n        if (!bulletNode) {\r\n            console.error(\"EmitterArc: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bulletNode.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n\r\n        BulletSystem.onCreateBullet(bullet);\r\n        \r\n        // Post set bullet properties\r\n        bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.mover.speed *= this.emitterData.emitPower;\r\n        // bulletNode.setRotationFromEuler(0, 0, radiansToDegrees(Math.atan2(direction.y, direction.x)));\r\n\r\n        return bulletNode;\r\n    }\r\n\r\n    // Implementation of CObject abstract methods\r\n    protected onObjectInit(): void {\r\n        // Override in subclasses if needed\r\n    }\r\n\r\n    protected onObjectDestroy(): void {\r\n        // Clean up any scheduled callbacks\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this.emitterData) {\r\n            return;\r\n        }\r\n\r\n        this.statusElapsedTime += deltaTime;\r\n        this.totalElapsedTime += deltaTime;\r\n\r\n        switch (this.status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting(deltaTime);\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this.statusElapsedTime >= this.emitterData.initialDelay) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.emitterData.isPreWarm)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this.statusElapsedTime >= this.emitterData.preWarmDuration) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting(deltaTime: number) {\r\n        if (this.statusElapsedTime > this.emitterData.emitDuration) {\r\n            this.stopEmitting();\r\n            if (this.emitterData.isLoop) \r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else \r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n        \r\n        // Start emitting if not already started\r\n        if (!this.isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this.isEmitting && this.statusElapsedTime >= this.nextEmitTime) {\r\n            // Check if it's time for the next emit\r\n            this.tryEmit();\r\n            this.scheduleNextEmit();\r\n        }\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this.statusElapsedTime >= this.emitterData.loopInterval) {\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n    }\r\n}\r\n"]}