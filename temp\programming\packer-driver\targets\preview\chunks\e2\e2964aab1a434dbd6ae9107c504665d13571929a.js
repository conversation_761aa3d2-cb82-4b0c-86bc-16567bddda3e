System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/EmitterArc.ts at runtime.
      throw new Error("Error: Error when fetching file E:M2GameClientassetsscriptsGameOldworldweaponEmitterArc.ts: Error: ENOENT: no such file or directory, open 'E:M2GameClientassetsscriptsGameOldworldweaponEmitterArc.ts'");
    }
  };
});
//# sourceMappingURL=e2964aab1a434dbd6ae9107c504665d13571929a.js.map