{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/ObjectPool.ts"], "names": ["ObjectPool", "instantiate", "NodePool", "getPool", "key", "all_pools", "clearPool", "pool", "clear", "clearAll", "getNode", "node_parent", "prefab", "uuid", "node", "size", "get", "parent", "pu<PERSON>", "returnNode", "console", "warn", "destroy", "put"], "mappings": ";;;yFAMaA,U;;;;;;;;;AANQC,MAAAA,W,OAAAA,W;AAA2BC,MAAAA,Q,OAAAA,Q;;;;;;;AAEhD;AACA;AACA;AACA;;;4BACaF,U,GAAN,MAAMA,UAAN,CAAiB;AAGC,eAAPG,OAAO,CAACC,GAAD,EAAwB;AACzC,cAAI,CAAC,KAAKC,SAAL,CAAeD,GAAf,CAAL,EAA0B;AACtB,iBAAKC,SAAL,CAAeD,GAAf,IAAsB,IAAIF,QAAJ,EAAtB;AACH;;AACD,iBAAO,KAAKG,SAAL,CAAeD,GAAf,CAAP;AACH;;AAEsB,eAATE,SAAS,CAACF,GAAD,EAAoB;AACvC,cAAMG,IAAI,GAAG,KAAKF,SAAL,CAAeD,GAAf,CAAb;;AACA,cAAIG,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACC,KAAL;AACH;AACJ;;AAEqB,eAARC,QAAQ,GAAS;AAC3B,eAAK,IAAML,IAAX,IAAkB,KAAKC,SAAvB,EAAkC;AAC9B,iBAAKC,SAAL,CAAeF,IAAf;AACH;;AACD,eAAKC,SAAL,GAAiB,EAAjB;AACH;;AAEoB,eAAPK,OAAO,CAACC,WAAD,EAAoBC,MAApB,EAAoC;AACrD,cAAIL,IAAI,GAAG,KAAKJ,OAAL,CAAaS,MAAM,CAACC,IAApB,CAAX;AAEA,cAAIC,IAAI,GAAG,IAAX;;AACA,cAAIP,IAAI,CAACQ,IAAL,KAAc,CAAlB,EAAqB;AACjB;AACAD,YAAAA,IAAI,GAAGP,IAAI,CAACS,GAAL,EAAP;AACH,WAHD,MAGO;AACH;AACAF,YAAAA,IAAI,GAAGb,WAAW,CAACW,MAAD,CAAlB;AACH;;AAEDE,UAAAA,IAAI,CAACG,MAAL,GAAcN,WAAd,CAZqD,CAY1B;AAC3B;;AACAG,UAAAA,IAAI,CAACI,KAAL,GAAaN,MAAM,CAACC,IAApB;AAEA,iBAAOC,IAAP;AACH;;AAEuB,eAAVK,UAAU,CAACL,IAAD,EAAa;AACjC;AACA,cAAI,CAACA,IAAI,CAACI,KAAV,EAAiB;AACbE,YAAAA,OAAO,CAACC,IAAR,CAAa,mCAAb;AACAP,YAAAA,IAAI,CAACQ,OAAL;AACA;AACH,WANgC,CAQjC;;;AACA,cAAIf,IAAI,GAAG,KAAKF,SAAL,CAAeS,IAAI,CAACI,KAApB,CAAX;;AACA,cAAIX,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACgB,GAAL,CAAST,IAAT;AACH,WAFD,MAGK;AACDA,YAAAA,IAAI,CAACQ,OAAL;AACH;AACJ;;AA3DmB,O;;AAAXtB,MAAAA,U,CACMK,S,GAAyC,E", "sourcesContent": ["import { _decorator, instantiate, Node, Prefab, NodePool } from \"cc\";\r\n\r\n/**\r\n * BulletSystem - manages all bullets in the game world\r\n * Handles bullet creation, movement, collision, and cleanup\r\n */\r\nexport class ObjectPool {\r\n    private static all_pools: { [key: string]: NodePool } = {};\r\n\r\n    public static getPool(key: string): NodePool {\r\n        if (!this.all_pools[key]) {\r\n            this.all_pools[key] = new NodePool();\r\n        }\r\n        return this.all_pools[key];\r\n    }\r\n\r\n    public static clearPool(key: string): void {\r\n        const pool = this.all_pools[key];\r\n        if (pool) {\r\n            pool.clear();\r\n        }\r\n    }\r\n\r\n    public static clearAll(): void {\r\n        for (const key in this.all_pools) {\r\n            this.clearPool(key);\r\n        }\r\n        this.all_pools = {};\r\n    }\r\n\r\n    public static getNode(node_parent: Node, prefab: Prefab) {\r\n        let pool = this.getPool(prefab.uuid);\r\n\r\n        let node = null;\r\n        if (pool.size() > 0) { \r\n            // use size method to check if there're nodes available in the pool\r\n            node = pool.get();\r\n        } else { \r\n            // if not enough node in the pool, we call cc.instantiate to create node\r\n            node = instantiate(prefab);\r\n        }\r\n\r\n        node.parent = node_parent; // add new enemy node to the node tree\r\n        //@ts-ignore\r\n        node.puuid = prefab.uuid;\r\n\r\n        return node;\r\n    }\r\n\r\n    public static returnNode(node: Node) {\r\n        //@ts-ignore\r\n        if (!node.puuid) {\r\n            console.warn(\"Node does not have a prefab UUID.\");\r\n            node.destroy();\r\n            return;\r\n        }\r\n        \r\n        //@ts-ignore\r\n        let pool = this.all_pools[node.puuid];\r\n        if (pool) {\r\n            pool.put(node);\r\n        }\r\n        else {\r\n            node.destroy();\r\n        }\r\n    }\r\n}\r\n"]}