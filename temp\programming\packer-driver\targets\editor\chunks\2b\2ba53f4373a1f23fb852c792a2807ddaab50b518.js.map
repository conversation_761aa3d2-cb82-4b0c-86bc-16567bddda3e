{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/move/DefaultMoveModifier.ts"], "names": ["_decorator", "eMoveModifier", "eEasing", "ccclass", "property", "DefaultMoveModifier", "constructor", "targetType", "targetValue", "duration", "easing", "Linear", "Speed", "startValue", "currentTime", "isInitialized", "isFinished", "initialize", "movable", "speed", "SpeedAngle", "speedAngle", "Acceleration", "acceleration", "AccelerationAngle", "accelerationAngle", "tick", "deltaTime", "tickMoveProperty", "currentValue", "progress", "Math", "min", "easedProgress", "applyEasing", "t", "InSine", "cos", "PI", "OutSine", "sin", "InOutSine", "InQuad", "OutQuad", "InOutQuad", "pow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACyBC,MAAAA,a,iBAAAA,a;AAAeC,MAAAA,O,iBAAAA,O;;;;;;;;;OAC3C;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;qCAGjBK,mB,WADZF,OAAO,CAAC,qBAAD,C,gBAAR,MACaE,mBADb,CAC0D;AAWtDC,QAAAA,WAAW,CAACC,UAAD,EAA4BC,WAA5B,EAAiDC,QAAjD,EAAmEC,MAAe,GAAG;AAAA;AAAA,gCAAQC,MAA7F,EAAqG;AAAA,eATzGJ,UASyG,GAT7E;AAAA;AAAA,8CAAcK,KAS+D;AAAA,eARzGJ,WAQyG,GARnF,GAQmF;AAAA,eAPzGC,QAOyG,GAPtF,GAOsF;AAAA,eANzGC,MAMyG,GANvF;AAAA;AAAA,kCAAQC,MAM+E;AAAA,eAJxGE,UAIwG,GAJnF,CAImF;AAAA,eAHxGC,WAGwG,GAHlF,CAGkF;AAAA,eAFxGC,aAEwG,GAF/E,KAE+E;AAC5G,eAAKR,UAAL,GAAkBA,UAAlB;AACA,eAAKC,WAAL,GAAmBA,WAAnB;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AACA,eAAKC,MAAL,GAAcA,MAAd;AACH;;AAEDM,QAAAA,UAAU,GAAY;AAClB,iBAAO,KAAKF,WAAL,IAAoB,KAAKL,QAAhC;AACH;;AAEDQ,QAAAA,UAAU,CAACC,OAAD,EAAoB;AAC1B,kBAAQ,KAAKX,UAAb;AACI,iBAAK;AAAA;AAAA,gDAAcK,KAAnB;AACI,mBAAKC,UAAL,GAAkBK,OAAO,CAACC,KAA1B;AACA;;AACJ,iBAAK;AAAA;AAAA,gDAAcC,UAAnB;AACI,mBAAKP,UAAL,GAAkBK,OAAO,CAACG,UAA1B;AACA;;AACJ,iBAAK;AAAA;AAAA,gDAAcC,YAAnB;AACI,mBAAKT,UAAL,GAAkBK,OAAO,CAACK,YAA1B;AACA;;AACJ,iBAAK;AAAA;AAAA,gDAAcC,iBAAnB;AACI,mBAAKX,UAAL,GAAkBK,OAAO,CAACO,iBAA1B;AACA;AAZR;;AAcA,eAAKV,aAAL,GAAqB,IAArB;AACH;;AAEDW,QAAAA,IAAI,CAACR,OAAD,EAAoBS,SAApB,EAA6C;AAC7C;AACA,cAAI,CAAC,KAAKZ,aAAV,EAAyB;AACrB,iBAAKE,UAAL,CAAgBC,OAAhB;AACH,WAJ4C,CAM7C;;;AACA,kBAAQ,KAAKX,UAAb;AACI,iBAAK;AAAA;AAAA,gDAAcK,KAAnB;AACIM,cAAAA,OAAO,CAACC,KAAR,GAAgB,KAAKS,gBAAL,CAAsBV,OAAO,CAACC,KAA9B,EAAqCQ,SAArC,CAAhB;AACA;;AACJ,iBAAK;AAAA;AAAA,gDAAcP,UAAnB;AACIF,cAAAA,OAAO,CAACG,UAAR,GAAqB,KAAKO,gBAAL,CAAsBV,OAAO,CAACG,UAA9B,EAA0CM,SAA1C,CAArB;AACA;;AACJ,iBAAK;AAAA;AAAA,gDAAcL,YAAnB;AACIJ,cAAAA,OAAO,CAACK,YAAR,GAAuB,KAAKK,gBAAL,CAAsBV,OAAO,CAACK,YAA9B,EAA4CI,SAA5C,CAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,gDAAcH,iBAAnB;AACIN,cAAAA,OAAO,CAACO,iBAAR,GAA4B,KAAKG,gBAAL,CAAsBV,OAAO,CAACO,iBAA9B,EAAiDE,SAAjD,CAA5B;AACA;AAZR;AAcH;;AAEDC,QAAAA,gBAAgB,CAACC,YAAD,EAAuBF,SAAvB,EAAkD;AAC9D;AACA,eAAKb,WAAL,IAAoBa,SAApB,CAF8D,CAI9D;;AACA,cAAIG,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAAS,GAAT,EAAc,KAAKlB,WAAL,GAAmB,KAAKL,QAAtC,CAAf,CAL8D,CAO9D;;AACA,cAAIwB,aAAa,GAAG5B,mBAAmB,CAAC6B,WAApB,CAAgC,KAAKxB,MAArC,EAA6CoB,QAA7C,CAApB,CAR8D,CAU9D;;AACA,iBAAO,KAAKjB,UAAL,GAAkB,CAAC,KAAKL,WAAL,GAAmB,KAAKK,UAAzB,IAAuCoB,aAAhE;AACH;;AAEiB,eAAXC,WAAW,CAACxB,MAAD,EAAkByB,CAAlB,EAAqC;AACnD,kBAAQzB,MAAR;AACI,iBAAK;AAAA;AAAA,oCAAQC,MAAb;AACI,qBAAOwB,CAAP;;AAEJ,iBAAK;AAAA;AAAA,oCAAQC,MAAb;AACI,qBAAO,IAAIL,IAAI,CAACM,GAAL,CAASF,CAAC,GAAGJ,IAAI,CAACO,EAAT,GAAc,CAAvB,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQC,OAAb;AACI,qBAAOR,IAAI,CAACS,GAAL,CAASL,CAAC,GAAGJ,IAAI,CAACO,EAAT,GAAc,CAAvB,CAAP;;AAEJ,iBAAK;AAAA;AAAA,oCAAQG,SAAb;AACI,qBAAO,EAAEV,IAAI,CAACM,GAAL,CAASN,IAAI,CAACO,EAAL,GAAUH,CAAnB,IAAwB,CAA1B,IAA+B,CAAtC;;AAEJ,iBAAK;AAAA;AAAA,oCAAQO,MAAb;AACI,qBAAOP,CAAC,GAAGA,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQQ,OAAb;AACI,qBAAO,IAAI,CAAC,IAAIR,CAAL,KAAW,IAAIA,CAAf,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQS,SAAb;AACI,qBAAOT,CAAC,GAAG,GAAJ,GAAU,IAAIA,CAAJ,GAAQA,CAAlB,GAAsB,IAAIJ,IAAI,CAACc,GAAL,CAAS,CAAC,CAAD,GAAKV,CAAL,GAAS,CAAlB,EAAqB,CAArB,IAA0B,CAA3D;;AAEJ;AACI,qBAAOA,CAAP;AAvBR;AAyBH;;AAvGqD,O", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport { IMovable, IMoveModifier, eMoveModifier, eEasing } from './IMovable';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('DefaultMoveModifier')\r\nexport class DefaultMoveModifier implements IMoveModifier {\r\n\r\n    public targetType: eMoveModifier = eMoveModifier.Speed;\r\n    public targetValue: number = 1.0\r\n    public duration: number = 1.0;\r\n    public easing: eEasing = eEasing.Linear;\r\n\r\n    private startValue: number = 0;\r\n    private currentTime: number = 0;\r\n    private isInitialized: boolean = false;\r\n\r\n    constructor(targetType: eMoveModifier, targetValue: number, duration: number, easing: eEasing = eEasing.Linear) {\r\n        this.targetType = targetType;\r\n        this.targetValue = targetValue;\r\n        this.duration = duration;\r\n        this.easing = easing;\r\n    }\r\n\r\n    isFinished(): boolean {\r\n        return this.currentTime >= this.duration;\r\n    }\r\n\r\n    initialize(movable: IMovable) {\r\n        switch (this.targetType) {\r\n            case eMoveModifier.Speed:\r\n                this.startValue = movable.speed;\r\n                break;\r\n            case eMoveModifier.SpeedAngle:\r\n                this.startValue = movable.speedAngle;\r\n                break;\r\n            case eMoveModifier.Acceleration:\r\n                this.startValue = movable.acceleration;\r\n                break;\r\n            case eMoveModifier.AccelerationAngle:\r\n                this.startValue = movable.accelerationAngle;\r\n                break;\r\n        }\r\n        this.isInitialized = true;\r\n    }\r\n\r\n    tick(movable: IMovable, deltaTime: number): void {\r\n        // Initialize start value on first tick\r\n        if (!this.isInitialized) {\r\n            this.initialize(movable);\r\n        }\r\n\r\n        // Update the appropriate property based on targetType\r\n        switch (this.targetType) {\r\n            case eMoveModifier.Speed:\r\n                movable.speed = this.tickMoveProperty(movable.speed, deltaTime);\r\n                break;\r\n            case eMoveModifier.SpeedAngle:\r\n                movable.speedAngle = this.tickMoveProperty(movable.speedAngle, deltaTime);\r\n                break;\r\n            case eMoveModifier.Acceleration:\r\n                movable.acceleration = this.tickMoveProperty(movable.acceleration, deltaTime);\r\n                break;\r\n            case eMoveModifier.AccelerationAngle:\r\n                movable.accelerationAngle = this.tickMoveProperty(movable.accelerationAngle, deltaTime);\r\n                break;\r\n        }\r\n    }\r\n\r\n    tickMoveProperty(currentValue: number, deltaTime: number): number {\r\n        // Update current time\r\n        this.currentTime += deltaTime;\r\n\r\n        // Calculate progress ratio (0 to 1)\r\n        let progress = Math.min(1.0, this.currentTime / this.duration);\r\n\r\n        // Apply easing function\r\n        let easedProgress = DefaultMoveModifier.applyEasing(this.easing, progress);\r\n\r\n        // Interpolate between start value and target value\r\n        return this.startValue + (this.targetValue - this.startValue) * easedProgress;\r\n    }\r\n\r\n    static applyEasing(easing: eEasing, t: number): number {\r\n        switch (easing) {\r\n            case eEasing.Linear:\r\n                return t;\r\n\r\n            case eEasing.InSine:\r\n                return 1 - Math.cos(t * Math.PI / 2);\r\n\r\n            case eEasing.OutSine:\r\n                return Math.sin(t * Math.PI / 2);\r\n\r\n            case eEasing.InOutSine:\r\n                return -(Math.cos(Math.PI * t) - 1) / 2;\r\n\r\n            case eEasing.InQuad:\r\n                return t * t;\r\n\r\n            case eEasing.OutQuad:\r\n                return 1 - (1 - t) * (1 - t);\r\n\r\n            case eEasing.InOutQuad:\r\n                return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;\r\n\r\n            default:\r\n                return t;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}