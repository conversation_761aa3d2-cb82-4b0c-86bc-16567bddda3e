{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Emitter.ts"], "names": ["_decorator", "instantiate", "Prefab", "Component", "ccclass", "executeInEditMode", "property", "playOnFocus", "eEmitterStatus", "Emitter", "type", "displayName", "updateInEditor", "status", "None", "statusElapsedTime", "totalElapsedTime", "isEmitting", "nextEmitTime", "resetInEditor", "onFocusInEditor", "onLostFocusInEditor", "i", "node", "children", "length", "child", "name", "destroy", "changeStatus", "scheduleNextEmit", "emitterData", "emitInterval", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "perEmitCount", "localIndex", "perEmitInterval", "scheduleOnce", "emitSingle", "tryEmit", "index", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "count", "arc", "radian", "angle", "Math", "PI", "x", "cos", "y", "sin", "radius", "bulletPrefab", "console", "warn", "bulletNode", "error", "emitterPos", "getWorldPosition", "setWorldPosition", "z", "parent", "<PERSON><PERSON><PERSON><PERSON>", "onObjectInit", "onObjectDestroy", "isInScreen", "update", "deltaTime", "updateStatusNone", "Prewarm", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted", "initialDelay", "isPreWarm", "preWarmDuration", "emitDuration", "isLoop", "loopInterval"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;;;;;;;;OAG1C;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA;AAAxC,O,GAAwDP,U;;gCAElDQ,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;yBAOCC,O,WAHZL,OAAO,CAAC,SAAD,C,UAKHE,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAER,MAAP;AAAeS,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,gBAJZN,iB,UACAE,W,qBAFD,MAGaE,OAHb,SAG6BN,SAH7B,CAGuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAQ5BS,cAR4B,GAQD,KARC;AAAA,eAkCzBC,MAlCyB,GAkCAL,cAAc,CAACM,IAlCf;AAAA,eAmCzBC,iBAnCyB,GAmCG,CAnCH;AAAA,eAoCzBC,gBApCyB,GAoCE,CApCF;AAAA,eAqCzBC,UArCyB,GAqCH,KArCG;AAAA,eAsCzBC,YAtCyB,GAsCF,CAtCE;AAAA;;AAQM;AAEzC;AACA;AACA;AACA;AACA;AAEOC,QAAAA,aAAa,GAAG;AACnB,eAAKP,cAAL,GAAsB,IAAtB;AACH;;AAEMQ,QAAAA,eAAe,GAAG;AACrB,eAAKR,cAAL,GAAsB,IAAtB;AACH;;AAEMS,QAAAA,mBAAmB,GAAG;AACzB,eAAKT,cAAL,GAAsB,KAAtB;;AACA,eAAK,IAAIU,CAAC,GAAG,KAAKC,IAAL,CAAUC,QAAV,CAAmBC,MAAnB,GAA4B,CAAzC,EAA4CH,CAAC,IAAI,CAAjD,EAAoDA,CAAC,EAArD,EAAyD;AACrD,kBAAMI,KAAK,GAAG,KAAKH,IAAL,CAAUC,QAAV,CAAmBF,CAAnB,CAAd;;AACA,gBAAII,KAAK,CAACC,IAAN,KAAe,UAAnB,EAA+B;AAC3BD,cAAAA,KAAK,CAACE,OAAN;AACH;AACJ;AACJ;;AAQD;AACJ;AACA;AACIC,QAAAA,YAAY,CAAChB,MAAD,EAAyB;AACjC,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKE,iBAAL,GAAyB,CAAzB;AACH;;AAESe,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAKZ,YAAL,GAAoB,KAAKH,iBAAL,GAAyB,KAAKgB,WAAL,CAAiBC,YAA9D;AACH;;AAESC,QAAAA,aAAa,GAAG;AACtB,eAAKhB,UAAL,GAAkB,IAAlB,CADsB,CAEtB;AACA;AACH;;AAESiB,QAAAA,YAAY,GAAG;AACrB,eAAKjB,UAAL,GAAkB,KAAlB,CADqB,CAErB;;AACA,eAAKkB,sBAAL;AACH;;AAESC,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,IAAI,GAAS;AACnB,eAAK,IAAIf,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKS,WAAL,CAAiBO,YAArC,EAAmDhB,CAAC,EAApD,EAAwD;AACpD,gBAAIiB,UAAU,GAAGjB,CAAjB;;AACA,gBAAI,KAAKS,WAAL,CAAiBS,eAAjB,GAAmC,CAAvC,EAA0C;AACtC,mBAAKC,YAAL,CAAkB,MAAM;AACpB,qBAAKC,UAAL,CAAgBH,UAAhB,EADoB,CAEpB;AACH,eAHD,EAGG,KAAKR,WAAL,CAAiBS,eAAjB,GAAmCD,UAHtC;AAIH;AACJ;AAEJ;;AAESI,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKP,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESK,QAAAA,UAAU,CAACE,KAAD,EAAe;AAC/B,gBAAMC,SAAS,GAAG,KAAKC,iBAAL,CAAuBF,KAAvB,CAAlB;AACA,gBAAMG,QAAQ,GAAG,KAAKC,gBAAL,CAAsBJ,KAAtB,CAAjB;AACA,eAAKK,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACF,KAAD,EAA0C;AACvD;AACA,gBAAMM,WAAW,GAAG,KAAKnB,WAAL,CAAiBoB,KAAjB,GAAyB,CAAzB,GAA8B,KAAKpB,WAAL,CAAiBqB,GAAjB,IAAwB,KAAKrB,WAAL,CAAiBoB,KAAjB,GAAyB,CAAjD,CAAD,GAAwDP,KAAxD,GAAgE,KAAKb,WAAL,CAAiBqB,GAAjB,GAAuB,CAApH,GAAwH,CAA5I;AACA,gBAAMC,MAAM,GAAG,CAAC,KAAKtB,WAAL,CAAiBuB,KAAjB,GAAyBJ,WAA1B,KAA0CK,IAAI,CAACC,EAAL,GAAU,GAApD,CAAf;AACA,iBAAO;AACHC,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT,CADA;AAEHM,YAAAA,CAAC,EAAEJ,IAAI,CAACK,GAAL,CAASP,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIL,QAAAA,gBAAgB,CAACJ,KAAD,EAA0C;AACtD,cAAI,KAAKb,WAAL,CAAiB8B,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,mBAAO;AAAEJ,cAAAA,CAAC,EAAE,CAAL;AAAQE,cAAAA,CAAC,EAAE;AAAX,aAAP;AACH;;AAED,gBAAMd,SAAS,GAAG,KAAKC,iBAAL,CAAuBF,KAAvB,CAAlB;AACA,iBAAO;AACHa,YAAAA,CAAC,EAAEZ,SAAS,CAACY,CAAV,GAAc,KAAK1B,WAAL,CAAiB8B,MAD/B;AAEHF,YAAAA,CAAC,EAAEd,SAAS,CAACc,CAAV,GAAc,KAAK5B,WAAL,CAAiB8B;AAF/B,WAAP;AAIH;;AAEDZ,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAA0E;AAAA;;AAClF,cAAI,CAAC,KAAKe,YAAV,EAAwB;AACpBC,YAAAA,OAAO,CAACC,IAAR,CAAa,uCAAb;AACA,mBAAO,IAAP;AACH,WAJiF,CAMlF;;;AACA,gBAAMC,UAAU,GAAGhE,WAAW,CAAC,KAAK6D,YAAN,CAA9B;;AACA,cAAI,CAACG,UAAL,EAAiB;AACbF,YAAAA,OAAO,CAACG,KAAR,CAAc,iDAAd;AACA,mBAAO,IAAP;AACH,WAXiF,CAalF;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AACA,gBAAMC,UAAU,GAAG,KAAK5C,IAAL,CAAU6C,gBAAV,EAAnB;AACAH,UAAAA,UAAU,CAACI,gBAAX,CACIF,UAAU,CAACV,CAAX,GAAeV,QAAQ,CAACU,CAD5B,EAEIU,UAAU,CAACR,CAAX,GAAeZ,QAAQ,CAACY,CAF5B,EAGIQ,UAAU,CAACG,CAHf;AAMA,oCAAK/C,IAAL,CAAUgD,MAAV,+BAAkBC,QAAlB,CAA2BP,UAA3B;AAEA,iBAAOA,UAAP;AACH,SArKkC,CAuKnC;;;AACUQ,QAAAA,YAAY,GAAS,CAC3B;AACH;;AAESC,QAAAA,eAAe,GAAS,CAC9B;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,MAAM,CAACC,SAAD,EAA0B;AACtC,cAAI,CAAC,KAAK9C,WAAV,EAAuB;AACnB;AACH;;AAED,eAAKhB,iBAAL,IAA0B8D,SAA1B;AACA,eAAK7D,gBAAL,IAAyB6D,SAAzB;;AAEA,kBAAQ,KAAKhE,MAAb;AAEI,iBAAKL,cAAc,CAACM,IAApB;AACI,mBAAKgE,gBAAL;AACA;;AACJ,iBAAKtE,cAAc,CAACuE,OAApB;AACI,mBAAKC,mBAAL;AACA;;AACJ,iBAAKxE,cAAc,CAACyE,QAApB;AACI,mBAAKC,oBAAL,CAA0BL,SAA1B;AACA;;AACJ,iBAAKrE,cAAc,CAAC2E,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAK5E,cAAc,CAAC6E,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;AAoBH;;AAESR,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAK/D,iBAAL,IAA0B,KAAKgB,WAAL,CAAiBwD,YAA/C,EAA6D;AACzD,iBAAK1D,YAAL,CAAkBrB,cAAc,CAACuE,OAAjC;AACH;AACJ;;AAESC,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKjD,WAAL,CAAiByD,SAAtB,EACI,KAAK3D,YAAL,CAAkBrB,cAAc,CAACyE,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAKlE,iBAAL,IAA0B,KAAKgB,WAAL,CAAiB0D,eAA/C,EAAgE;AAC5D,mBAAK5D,YAAL,CAAkBrB,cAAc,CAACyE,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,CAACL,SAAD,EAAoB;AAC9C,cAAI,KAAK9D,iBAAL,GAAyB,KAAKgB,WAAL,CAAiB2D,YAA9C,EAA4D;AACxD,iBAAKxD,YAAL;AACA,gBAAI,KAAKH,WAAL,CAAiB4D,MAArB,EACI,KAAK9D,YAAL,CAAkBrB,cAAc,CAAC2E,cAAjC,EADJ,KAGI,KAAKtD,YAAL,CAAkBrB,cAAc,CAAC6E,SAAjC;AACJ;AACH,WAR6C,CAU9C;;;AACA,cAAI,CAAC,KAAKpE,UAAV,EAAsB;AAClB,iBAAKgB,aAAL;AACH,WAFD,MAGK,IAAI,KAAKhB,UAAL,IAAmB,KAAKF,iBAAL,IAA0B,KAAKG,YAAtD,EAAoE;AACrE;AACA,iBAAKyB,OAAL;AACA,iBAAKb,gBAAL;AACH;AACJ;;AAESsD,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKrE,iBAAL,IAA0B,KAAKgB,WAAL,CAAiB6D,YAA/C,EAA6D;AACzD,iBAAK/D,YAAL,CAAkBrB,cAAc,CAACyE,QAAjC;AACH;AACJ;;AAESK,QAAAA,qBAAqB,GAAG,CAC9B;AACH;;AAnQkC,O;;;;;iBAGZ,I;;sFAEtBhF,Q;;;;;iBAC0B,I", "sourcesContent": ["import { _decorator, instantiate, Node, Prefab, Component } from 'cc';\r\nimport { Weapon } from './Weapon';\r\nimport { EmitterData } from '../../../Game/data/EmitterData';\r\nconst { ccclass, executeInEditMode, property, playOnFocus } = _decorator;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n@ccclass('Emitter')\r\n@executeInEditMode\r\n@playOnFocus\r\nexport class Emitter extends Component {\r\n\r\n    @property({type: Prefab, displayName: \"Bullet Prefab\"})\r\n    bulletPrefab: Prefab = null;\r\n\r\n    @property\r\n    emitterData: EmitterData = null;\r\n\r\n    public updateInEditor : boolean = false; // 是否在编辑器中更新\r\n\r\n    // protected update(dt): void {\r\n    //     if (CC_EDITOR && this.updateInEditor) {\r\n    //         LQBulletSystem.update_logic(dt);\r\n    //     }\r\n    // }\r\n\r\n    public resetInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onFocusInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onLostFocusInEditor() {\r\n        this.updateInEditor = false;\r\n        for (let i = this.node.children.length - 1; i >= 0; i--) {\r\n            const child = this.node.children[i];\r\n            if (child.name === '_bullet_') {\r\n                child.destroy();\r\n            }\r\n        }\r\n    }\r\n\r\n    protected status: eEmitterStatus = eEmitterStatus.None;\r\n    protected statusElapsedTime: number = 0;\r\n    protected totalElapsedTime: number = 0;\r\n    protected isEmitting: boolean = false;\r\n    protected nextEmitTime: number = 0;\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        this.status = status;\r\n        this.statusElapsedTime = 0;\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // Schedule the next emit after emitInterval\r\n        this.nextEmitTime = this.statusElapsedTime + this.emitterData.emitInterval;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this.isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this.isEmitting = false;\r\n        // Clear any scheduled per-emit bullets\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * TODO: implement bullet emission logic in subclasses\r\n     */\r\n    protected emit(): void {\r\n        for (let i = 0; i < this.emitterData.perEmitCount; i++) {\r\n            let localIndex = i;\r\n            if (this.emitterData.perEmitInterval > 0) {\r\n                this.scheduleOnce(() => {\r\n                    this.emitSingle(localIndex);\r\n                    // Logic to emit the bullet after the interval\r\n                }, this.emitterData.perEmitInterval * localIndex);\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number) {\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.emitterData.count > 1 ? (this.emitterData.arc / (this.emitterData.count - 1)) * index - this.emitterData.arc / 2 : 0;\r\n        const radian = (this.emitterData.angle + angleOffset) * (Math.PI / 180);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number): { x: number, y: number } {\r\n        if (this.emitterData.radius <= 0) {\r\n            return { x: 0, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.emitterData.radius,\r\n            y: direction.y * this.emitterData.radius\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        if (!this.bulletPrefab) {\r\n            console.warn(\"EmitterArc: No bullet prefab assigned\");\r\n            return null;\r\n        }\r\n\r\n        // Instantiate the bullet from prefab\r\n        const bulletNode = instantiate(this.bulletPrefab);\r\n        if (!bulletNode) {\r\n            console.error(\"EmitterArc: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        // const bullet = bulletNode.getComponent(Bullet);\r\n        // if (!bullet) {\r\n        //     console.error(\"EmitterArc: Bullet prefab does not have Bullet component\");\r\n        //     bulletNode.destroy();\r\n        //     return null;\r\n        // }\r\n\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bulletNode.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n\r\n        this.node.parent?.addChild(bulletNode);\r\n\r\n        return bulletNode;\r\n    }\r\n\r\n    // Implementation of CObject abstract methods\r\n    protected onObjectInit(): void {\r\n        // Override in subclasses if needed\r\n    }\r\n\r\n    protected onObjectDestroy(): void {\r\n        // Clean up any scheduled callbacks\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    protected update(deltaTime: number): void {\r\n        if (!this.emitterData) {\r\n            return;\r\n        }\r\n\r\n        this.statusElapsedTime += deltaTime;\r\n        this.totalElapsedTime += deltaTime;\r\n\r\n        switch (this.status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting(deltaTime);\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this.statusElapsedTime >= this.emitterData.initialDelay) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.emitterData.isPreWarm)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this.statusElapsedTime >= this.emitterData.preWarmDuration) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting(deltaTime: number) {\r\n        if (this.statusElapsedTime > this.emitterData.emitDuration) {\r\n            this.stopEmitting();\r\n            if (this.emitterData.isLoop) \r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else \r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n        \r\n        // Start emitting if not already started\r\n        if (!this.isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this.isEmitting && this.statusElapsedTime >= this.nextEmitTime) {\r\n            // Check if it's time for the next emit\r\n            this.tryEmit();\r\n            this.scheduleNextEmit();\r\n        }\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this.statusElapsedTime >= this.emitterData.loopInterval) {\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n    }\r\n}\r\n"]}