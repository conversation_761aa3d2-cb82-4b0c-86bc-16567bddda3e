{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Emitter.ts"], "names": ["_decorator", "Prefab", "Component", "EDITOR", "Bullet", "EmitterData", "ObjectPool", "BulletSystem", "ccclass", "executeInEditMode", "property", "playOnFocus", "eEmitterStatus", "Emitter", "type", "displayName", "updateInEditor", "status", "None", "statusElapsedTime", "totalElapsedTime", "isEmitting", "nextEmitTime", "start", "onCreateEmitter", "update", "dt", "tick", "tickBullets", "resetInEditor", "onFocusInEditor", "onLostFocusInEditor", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "destroyAllBullets", "changeStatus", "scheduleNextEmit", "emitterData", "emitInterval", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "i", "perEmitCount", "localIndex", "perEmitInterval", "scheduleOnce", "emitSingle", "tryEmit", "index", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "count", "arc", "radian", "angle", "Math", "PI", "x", "cos", "y", "sin", "radius", "bulletPrefab", "console", "warn", "bulletNode", "getNode", "error", "bullet", "getComponent", "destroy", "name", "kBulletNameInEditor", "onCreateBullet", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "onObjectInit", "onObjectDestroy", "isInScreen", "deltaTime", "updateStatusNone", "Prewarm", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted", "initialDelay", "isPreWarm", "preWarmDuration", "emitDuration", "isLoop", "loopInterval"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA+BC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AACvCC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA;AAAxC,O,GAAwDX,U;;gCAElDY,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;yBAOCC,O,WAHZL,OAAO,CAAC,SAAD,C,UAOHE,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAEb,MAAP;AAAec,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAGRL,QAAQ,CAAC;AAACI,QAAAA,IAAI;AAAA;AAAA,sCAAL;AAAoBC,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,gBATZN,iB,UACAE,W,gCAFD,MAGaE,OAHb,SAG6BX,SAH7B,CAGuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAU5Bc,cAV4B,GAUD,KAVC;AAAA,eAuCzBC,MAvCyB,GAuCAL,cAAc,CAACM,IAvCf;AAAA,eAwCzBC,iBAxCyB,GAwCG,CAxCH;AAAA,eAyCzBC,gBAzCyB,GAyCE,CAzCF;AAAA,eA0CzBC,UA1CyB,GA0CH,KA1CG;AAAA,eA2CzBC,YA3CyB,GA2CF,CA3CE;AAAA;;AAUM;AAE/BC,QAAAA,KAAK,GAAU;AACrB;AAAA;AAAA,4CAAaC,eAAb,CAA6B,IAA7B;AACH;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAoB;AAChC,cAAIvB,MAAM,IAAI,KAAKa,cAAnB,EAAmC;AAC/B,iBAAKW,IAAL,CAAUD,EAAV;AACA;AAAA;AAAA,8CAAaE,WAAb,CAAyBF,EAAzB;AACH;AACJ;;AAEMG,QAAAA,aAAa,GAAG;AACnB,eAAKb,cAAL,GAAsB,IAAtB;AACH;;AAEMc,QAAAA,eAAe,GAAG;AACrB,eAAKd,cAAL,GAAsB,IAAtB;AACH;;AAEMe,QAAAA,mBAAmB,GAAG;AACzB,eAAKf,cAAL,GAAsB,KAAtB;;AAEA,cAAI;AAAA;AAAA,4CAAagB,YAAb,IAA6B;AAAA;AAAA,4CAAaA,YAAb,CAA0BC,OAA3D,EAAoE;AAChE;AAAA;AAAA,8CAAaC,iBAAb;AACH;AACJ;;AAQD;AACJ;AACA;AACIC,QAAAA,YAAY,CAAClB,MAAD,EAAyB;AACjC,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKE,iBAAL,GAAyB,CAAzB;AACH;;AAESiB,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAKd,YAAL,GAAoB,KAAKH,iBAAL,GAAyB,KAAKkB,WAAL,CAAiBC,YAA9D;AACH;;AAESC,QAAAA,aAAa,GAAG;AACtB,eAAKlB,UAAL,GAAkB,IAAlB,CADsB,CAEtB;AACA;AACH;;AAESmB,QAAAA,YAAY,GAAG;AACrB,eAAKnB,UAAL,GAAkB,KAAlB,CADqB,CAErB;;AACA,eAAKoB,sBAAL;AACH;;AAESC,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKP,WAAL,CAAiBQ,YAArC,EAAmDD,CAAC,EAApD,EAAwD;AACpD,gBAAIE,UAAU,GAAGF,CAAjB;;AACA,gBAAI,KAAKP,WAAL,CAAiBU,eAAjB,GAAmC,CAAvC,EAA0C;AACtC,mBAAKC,YAAL,CAAkB,MAAM;AACpB,qBAAKC,UAAL,CAAgBH,UAAhB,EADoB,CAEpB;AACH,eAHD,EAGG,KAAKT,WAAL,CAAiBU,eAAjB,GAAmCD,UAHtC;AAIH;AACJ;AAEJ;;AAESI,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKR,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESM,QAAAA,UAAU,CAACE,KAAD,EAAe;AAC/B,gBAAMC,SAAS,GAAG,KAAKC,iBAAL,CAAuBF,KAAvB,CAAlB;AACA,gBAAMG,QAAQ,GAAG,KAAKC,gBAAL,CAAsBJ,KAAtB,CAAjB;AACA,eAAKK,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACF,KAAD,EAA0C;AACvD;AACA,gBAAMM,WAAW,GAAG,KAAKpB,WAAL,CAAiBqB,KAAjB,GAAyB,CAAzB,GAA8B,KAAKrB,WAAL,CAAiBsB,GAAjB,IAAwB,KAAKtB,WAAL,CAAiBqB,KAAjB,GAAyB,CAAjD,CAAD,GAAwDP,KAAxD,GAAgE,KAAKd,WAAL,CAAiBsB,GAAjB,GAAuB,CAApH,GAAwH,CAA5I;AACA,gBAAMC,MAAM,GAAG,CAAC,KAAKvB,WAAL,CAAiBwB,KAAjB,GAAyBJ,WAA1B,KAA0CK,IAAI,CAACC,EAAL,GAAU,GAApD,CAAf;AACA,iBAAO;AACHC,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT,CADA;AAEHM,YAAAA,CAAC,EAAEJ,IAAI,CAACK,GAAL,CAASP,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIL,QAAAA,gBAAgB,CAACJ,KAAD,EAA0C;AACtD,cAAI,KAAKd,WAAL,CAAiB+B,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,mBAAO;AAAEJ,cAAAA,CAAC,EAAE,CAAL;AAAQE,cAAAA,CAAC,EAAE;AAAX,aAAP;AACH;;AAED,gBAAMd,SAAS,GAAG,KAAKC,iBAAL,CAAuBF,KAAvB,CAAlB;AACA,iBAAO;AACHa,YAAAA,CAAC,EAAEZ,SAAS,CAACY,CAAV,GAAc,KAAK3B,WAAL,CAAiB+B,MAD/B;AAEHF,YAAAA,CAAC,EAAEd,SAAS,CAACc,CAAV,GAAc,KAAK7B,WAAL,CAAiB+B;AAF/B,WAAP;AAIH;;AAEDZ,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAA0E;AAClF,cAAI,CAAC,KAAKe,YAAV,EAAwB;AACpBC,YAAAA,OAAO,CAACC,IAAR,CAAa,uCAAb;AACA,mBAAO,IAAP;AACH;;AAED,gBAAMC,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAazC,YAAhC,EAA8C,KAAKqC,YAAnD,CAAnB;;AACA,cAAI,CAACG,UAAL,EAAiB;AACbF,YAAAA,OAAO,CAACI,KAAR,CAAc,iDAAd;AACA,mBAAO,IAAP;AACH,WAViF,CAYlF;;;AACA,gBAAMC,MAAM,GAAGH,UAAU,CAACI,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAACD,MAAL,EAAa;AACTL,YAAAA,OAAO,CAACI,KAAR,CAAc,uDAAd;AACAF,YAAAA,UAAU,CAACK,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAI1E,MAAJ,EAAY;AACRqE,YAAAA,UAAU,CAACM,IAAX,GAAkBjE,OAAO,CAACkE,mBAA1B;AACH;;AAED;AAAA;AAAA,4CAAaC,cAAb,CAA4BL,MAA5B,EAxBkF,CAyBlF;;AACA,gBAAMM,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAX,UAAAA,UAAU,CAACY,gBAAX,CACIH,UAAU,CAACjB,CAAX,GAAeV,QAAQ,CAACU,CAD5B,EAEIiB,UAAU,CAACf,CAAX,GAAeZ,QAAQ,CAACY,CAF5B,EAGIe,UAAU,CAACI,CAHf;AAMA,iBAAOb,UAAP;AACH,SAzKkC,CA2KnC;;;AACUc,QAAAA,YAAY,GAAS,CAC3B;AACH;;AAESC,QAAAA,eAAe,GAAS,CAC9B;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAEM7D,QAAAA,IAAI,CAAC8D,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAKpD,WAAV,EAAuB;AACnB;AACH;;AAED,eAAKlB,iBAAL,IAA0BsE,SAA1B;AACA,eAAKrE,gBAAL,IAAyBqE,SAAzB;;AAEA,kBAAQ,KAAKxE,MAAb;AAEI,iBAAKL,cAAc,CAACM,IAApB;AACI,mBAAKwE,gBAAL;AACA;;AACJ,iBAAK9E,cAAc,CAAC+E,OAApB;AACI,mBAAKC,mBAAL;AACA;;AACJ,iBAAKhF,cAAc,CAACiF,QAApB;AACI,mBAAKC,oBAAL,CAA0BL,SAA1B;AACA;;AACJ,iBAAK7E,cAAc,CAACmF,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAKpF,cAAc,CAACqF,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;AAoBH;;AAESR,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAKvE,iBAAL,IAA0B,KAAKkB,WAAL,CAAiB8D,YAA/C,EAA6D;AACzD,iBAAKhE,YAAL,CAAkBvB,cAAc,CAAC+E,OAAjC;AACH;AACJ;;AAESC,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKvD,WAAL,CAAiB+D,SAAtB,EACI,KAAKjE,YAAL,CAAkBvB,cAAc,CAACiF,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAK1E,iBAAL,IAA0B,KAAKkB,WAAL,CAAiBgE,eAA/C,EAAgE;AAC5D,mBAAKlE,YAAL,CAAkBvB,cAAc,CAACiF,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,CAACL,SAAD,EAAoB;AAC9C,cAAI,KAAKtE,iBAAL,GAAyB,KAAKkB,WAAL,CAAiBiE,YAA9C,EAA4D;AACxD,iBAAK9D,YAAL;AACA,gBAAI,KAAKH,WAAL,CAAiBkE,MAArB,EACI,KAAKpE,YAAL,CAAkBvB,cAAc,CAACmF,cAAjC,EADJ,KAGI,KAAK5D,YAAL,CAAkBvB,cAAc,CAACqF,SAAjC;AACJ;AACH,WAR6C,CAU9C;;;AACA,cAAI,CAAC,KAAK5E,UAAV,EAAsB;AAClB,iBAAKkB,aAAL;AACH,WAFD,MAGK,IAAI,KAAKlB,UAAL,IAAmB,KAAKF,iBAAL,IAA0B,KAAKG,YAAtD,EAAoE;AACrE;AACA,iBAAK4B,OAAL;AACA,iBAAKd,gBAAL;AACH;AACJ;;AAES4D,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAK7E,iBAAL,IAA0B,KAAKkB,WAAL,CAAiBmE,YAA/C,EAA6D;AACzD,iBAAKrE,YAAL,CAAkBvB,cAAc,CAACiF,QAAjC;AACH;AACJ;;AAESK,QAAAA,qBAAqB,GAAG,CAC9B;AACH;;AAvQkC,O,UAE5BnB,mB,GAA6B,U;;;;;iBAGb,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, instantiate, Node, Prefab, Component } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { Bullet } from './Bullet';\r\nimport { EmitterData } from '../../../Game/data/EmitterData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { BulletSystem } from './BulletSystem';\r\nconst { ccclass, executeInEditMode, property, playOnFocus } = _decorator;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n@ccclass('Emitter')\r\n@executeInEditMode\r\n@playOnFocus\r\nexport class Emitter extends Component {\r\n\r\n    static kBulletNameInEditor:string = \"_bullet_\";\r\n\r\n    @property({type: Prefab, displayName: \"Bullet Prefab\"})\r\n    bulletPrefab: Prefab = null;\r\n\r\n    @property({type: EmitterData, displayName: \"Emitter Data\"})\r\n    emitterData: EmitterData = null;\r\n\r\n    public updateInEditor : boolean = false; // 是否在编辑器中更新\r\n\r\n    protected start() : void {\r\n        BulletSystem.onCreateEmitter(this);\r\n    }\r\n\r\n    protected update(dt : number): void {\r\n        if (EDITOR && this.updateInEditor) {\r\n            this.tick(dt);\r\n            BulletSystem.tickBullets(dt);\r\n        }\r\n    }\r\n\r\n    public resetInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onFocusInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onLostFocusInEditor() {\r\n        this.updateInEditor = false;\r\n\r\n        if (BulletSystem.bulletParent && BulletSystem.bulletParent.isValid) {\r\n            BulletSystem.destroyAllBullets()\r\n        }\r\n    }\r\n\r\n    protected status: eEmitterStatus = eEmitterStatus.None;\r\n    protected statusElapsedTime: number = 0;\r\n    protected totalElapsedTime: number = 0;\r\n    protected isEmitting: boolean = false;\r\n    protected nextEmitTime: number = 0;\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        this.status = status;\r\n        this.statusElapsedTime = 0;\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // Schedule the next emit after emitInterval\r\n        this.nextEmitTime = this.statusElapsedTime + this.emitterData.emitInterval;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this.isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this.isEmitting = false;\r\n        // Clear any scheduled per-emit bullets\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        for (let i = 0; i < this.emitterData.perEmitCount; i++) {\r\n            let localIndex = i;\r\n            if (this.emitterData.perEmitInterval > 0) {\r\n                this.scheduleOnce(() => {\r\n                    this.emitSingle(localIndex);\r\n                    // Logic to emit the bullet after the interval\r\n                }, this.emitterData.perEmitInterval * localIndex);\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number) {\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.emitterData.count > 1 ? (this.emitterData.arc / (this.emitterData.count - 1)) * index - this.emitterData.arc / 2 : 0;\r\n        const radian = (this.emitterData.angle + angleOffset) * (Math.PI / 180);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number): { x: number, y: number } {\r\n        if (this.emitterData.radius <= 0) {\r\n            return { x: 0, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.emitterData.radius,\r\n            y: direction.y * this.emitterData.radius\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        if (!this.bulletPrefab) {\r\n            console.warn(\"EmitterArc: No bullet prefab assigned\");\r\n            return null;\r\n        }\r\n\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this.bulletPrefab);\r\n        if (!bulletNode) {\r\n            console.error(\"EmitterArc: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        BulletSystem.onCreateBullet(bullet);\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bulletNode.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n\r\n        return bulletNode;\r\n    }\r\n\r\n    // Implementation of CObject abstract methods\r\n    protected onObjectInit(): void {\r\n        // Override in subclasses if needed\r\n    }\r\n\r\n    protected onObjectDestroy(): void {\r\n        // Clean up any scheduled callbacks\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this.emitterData) {\r\n            return;\r\n        }\r\n\r\n        this.statusElapsedTime += deltaTime;\r\n        this.totalElapsedTime += deltaTime;\r\n\r\n        switch (this.status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting(deltaTime);\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this.statusElapsedTime >= this.emitterData.initialDelay) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.emitterData.isPreWarm)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this.statusElapsedTime >= this.emitterData.preWarmDuration) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting(deltaTime: number) {\r\n        if (this.statusElapsedTime > this.emitterData.emitDuration) {\r\n            this.stopEmitting();\r\n            if (this.emitterData.isLoop) \r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else \r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n        \r\n        // Start emitting if not already started\r\n        if (!this.isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this.isEmitting && this.statusElapsedTime >= this.nextEmitTime) {\r\n            // Check if it's time for the next emit\r\n            this.tryEmit();\r\n            this.scheduleNextEmit();\r\n        }\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this.statusElapsedTime >= this.emitterData.loopInterval) {\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n    }\r\n}\r\n"]}