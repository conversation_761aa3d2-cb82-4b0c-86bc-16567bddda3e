{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/move/Movable.ts"], "names": ["_decorator", "misc", "Component", "Vec3", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "Movable", "isFacingMoveDir", "speed", "speedAngle", "acceleration", "accelerationAngle", "isVisible", "onBecomeVisible", "onBecomeInvisible", "activeModifiers", "Map", "addSolver", "solver", "set", "targetType", "tick", "dt", "for<PERSON>ach", "key", "lastPos", "node", "getPosition", "velocityX", "Math", "cos", "velocityY", "sin", "accelerationX", "accelerationY", "newVelocityX", "newVelocityY", "sqrt", "newPos", "x", "y", "z", "setPosition", "setRotationFromEuler", "isFinished", "delete", "checkVisibility", "setVisible", "visible"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;;;;;;;;;OACtC;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCJ,I;OACzC;AAAEK,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CR,U;;yBAKpCS,O,WAFZH,OAAO,CAAC,SAAD,C,gBACPE,iB,UADD,MAEaC,OAFb,SAE6BP,SAF7B,CAE2D;AAAA;AAAA;AAAA,eAEhDQ,eAFgD,GAErB,KAFqB;AAEd;AAFc,eAGhDC,KAHgD;AAGjB;AAHiB,eAIhDC,UAJgD;AAIjB;AAJiB,eAKhDC,YALgD;AAKjB;AALiB,eAMhDC,iBANgD;AAMjB;AANiB,eAQ/CC,SAR+C,GAQ1B,KAR0B;AAQjB;AARiB,eAShDC,eATgD,GASb,IATa;AAAA,eAUhDC,iBAVgD,GAUX,IAVW;AAYvD;AAZuD,eAa/CC,eAb+C,GAaM,IAAIC,GAAJ,EAbN;AAAA;;AAehDC,QAAAA,SAAS,CAACC,MAAD,EAAwB;AACpC,eAAKH,eAAL,CAAqBI,GAArB,CAAyBD,MAAM,CAACE,UAAhC,EAA4CF,MAA5C;AACH;;AAEMG,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,eAAKP,eAAL,CAAqBQ,OAArB,CAA6B,CAACL,MAAD,EAASM,GAAT,KACzBN,MAAM,CAACG,IAAP,CAAY,IAAZ,EAAkBC,EAAlB,CADJ,EAD0B,CAK1B;;AACA,gBAAMG,OAAO,GAAG,KAAKC,IAAL,CAAUC,WAAV,EAAhB,CAN0B,CAQ1B;;AACA,gBAAMC,SAAS,GAAG,KAAKpB,KAAL,GAAaqB,IAAI,CAACC,GAAL,CAAS7B,gBAAgB,CAAC,KAAKQ,UAAN,CAAzB,CAA/B;AACA,gBAAMsB,SAAS,GAAG,KAAKvB,KAAL,GAAaqB,IAAI,CAACG,GAAL,CAAS/B,gBAAgB,CAAC,KAAKQ,UAAN,CAAzB,CAA/B,CAV0B,CAY1B;;AACA,gBAAMwB,aAAa,GAAG,KAAKvB,YAAL,GAAoBmB,IAAI,CAACC,GAAL,CAAS7B,gBAAgB,CAAC,KAAKU,iBAAN,CAAzB,CAA1C;AACA,gBAAMuB,aAAa,GAAG,KAAKxB,YAAL,GAAoBmB,IAAI,CAACG,GAAL,CAAS/B,gBAAgB,CAAC,KAAKU,iBAAN,CAAzB,CAA1C,CAd0B,CAgB1B;;AACA,gBAAMwB,YAAY,GAAGP,SAAS,GAAGK,aAAa,GAAGX,EAAjD;AACA,gBAAMc,YAAY,GAAGL,SAAS,GAAGG,aAAa,GAAGZ,EAAjD,CAlB0B,CAoB1B;;AACA,eAAKd,KAAL,GAAaqB,IAAI,CAACQ,IAAL,CAAUF,YAAY,GAAGA,YAAf,GAA8BC,YAAY,GAAGA,YAAvD,CAAb,CArB0B,CAsB1B;AAEA;;AACA,gBAAME,MAAM,GAAG,IAAItC,IAAJ,CACXyB,OAAO,CAACc,CAAR,GAAYJ,YAAY,GAAGb,EADhB,EAEXG,OAAO,CAACe,CAAR,GAAYJ,YAAY,GAAGd,EAFhB,EAGXG,OAAO,CAACgB,CAHG,CAAf;AAKA,eAAKf,IAAL,CAAUgB,WAAV,CAAsBJ,MAAtB;;AAEA,cAAI,KAAK/B,eAAT,EAA0B;AACtB,iBAAKmB,IAAL,CAAUiB,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAKlC,UAA1C;AACH,WAlCyB,CAoC1B;;;AACA,eAAKM,eAAL,CAAqBQ,OAArB,CAA6B,CAACL,MAAD,EAASM,GAAT,KAAiB;AAC1C,gBAAIN,MAAM,CAAC0B,UAAP,EAAJ,EAAyB;AACrB,mBAAK7B,eAAL,CAAqB8B,MAArB,CAA4BrB,GAA5B;AACH;AACJ,WAJD;AAKH;AAED;AACJ;AACA;;;AACWsB,QAAAA,eAAe,GAAS;AAC3B,gBAAMlC,SAAS,GAAG,IAAlB,CAD2B,CACJ;;AACvB,eAAKmC,UAAL,CAAgBnC,SAAhB;AACH;;AAEMmC,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,cAAI,KAAKpC,SAAL,KAAmBoC,OAAvB,EAAgC;AAEhC,eAAKpC,SAAL,GAAiBoC,OAAjB;;AACA,cAAIA,OAAO,IAAI,KAAKnC,eAApB,EAAqC;AACjC,iBAAKA,eAAL;AACH,WAFD,MAEO,IAAI,CAACmC,OAAD,IAAY,KAAKlC,iBAArB,EAAwC;AAC3C,iBAAKA,iBAAL;AACH;AACJ;;AAhFsD,O", "sourcesContent": ["import { _decorator, misc, Component, Node, Vec3 } from 'cc';\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { IMovable, IMoveModifier, eMoveModifier } from './IMovable';\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    public isFacingMoveDir: boolean = false; // 是否朝向行进方向\r\n    public speed: number;                 // 速度\r\n    public speedAngle: number;            // 速度方向 (用角度表示)\r\n    public acceleration: number;          // 加速度\r\n    public accelerationAngle: number;     // 加速度方向 (用角度表示)\r\n\r\n    private isVisible: boolean = false;   // 是否可见\r\n    public onBecomeVisible: Function | null = null;\r\n    public onBecomeInvisible: Function | null = null;\r\n\r\n    // make a map by eMoveModifier\r\n    private activeModifiers: Map<eMoveModifier, IMoveModifier> = new Map();\r\n\r\n    public addSolver(solver: IMoveModifier) {\r\n        this.activeModifiers.set(solver.targetType, solver);\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        this.activeModifiers.forEach((solver, key) => \r\n            solver.tick(this, dt)\r\n        );\r\n\r\n        // 根据移动属性更新位置\r\n        const lastPos = this.node.getPosition();\r\n\r\n        // Convert speed and angle to velocity vector\r\n        const velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n        const velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n\r\n        // Convert acceleration and angle to acceleration vector\r\n        const accelerationX = this.acceleration * Math.cos(degreesToRadians(this.accelerationAngle));\r\n        const accelerationY = this.acceleration * Math.sin(degreesToRadians(this.accelerationAngle));\r\n\r\n        // Update velocity vector: v = v + a * dt\r\n        const newVelocityX = velocityX + accelerationX * dt;\r\n        const newVelocityY = velocityY + accelerationY * dt;\r\n\r\n        // Convert back to speed and angle\r\n        this.speed = Math.sqrt(newVelocityX * newVelocityX + newVelocityY * newVelocityY);\r\n        //this.speedAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));\r\n\r\n        // Update position: p = p + v * dt\r\n        const newPos = new Vec3(\r\n            lastPos.x + newVelocityX * dt,\r\n            lastPos.y + newVelocityY * dt,\r\n            lastPos.z\r\n        );\r\n        this.node.setPosition(newPos);\r\n\r\n        if (this.isFacingMoveDir) {\r\n            this.node.setRotationFromEuler(0, 0, this.speedAngle);\r\n        }\r\n\r\n        // remove finished solvers\r\n        this.activeModifiers.forEach((solver, key) => {\r\n            if (solver.isFinished()) {\r\n                this.activeModifiers.delete(key);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * TODO: 如果后续自己写碰撞，这里可以直接查询格子坐标来获取当前是否在屏幕内\r\n     */\r\n    public checkVisibility(): void {\r\n        const isVisible = true;// && this.node.getWorldBoundingBox().intersects(this.node.getScene().getBoundingBox());\r\n        this.setVisible(isVisible);\r\n    }\r\n\r\n    public setVisible(visible: boolean) {\r\n        if (this.isVisible === visible) return;\r\n\r\n        this.isVisible = visible;\r\n        if (visible && this.onBecomeVisible) {\r\n            this.onBecomeVisible();\r\n        } else if (!visible && this.onBecomeInvisible) {\r\n            this.onBecomeInvisible();\r\n        }\r\n    }\r\n}"]}