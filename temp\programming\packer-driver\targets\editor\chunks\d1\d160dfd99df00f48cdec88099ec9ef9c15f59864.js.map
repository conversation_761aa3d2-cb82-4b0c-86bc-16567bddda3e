{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/move/Movable.ts"], "names": ["_decorator", "Component", "Vec3", "ccclass", "property", "executeInEditMode", "Movable", "speed", "speedAngle", "acceleration", "accelerationAngle", "isVisible", "onBecomeVisible", "onBecomeInvisible", "activeModifiers", "Map", "addSolver", "solver", "set", "targetType", "tick", "dt", "for<PERSON>ach", "key", "lastPos", "node", "getPosition", "console", "log", "newPos", "x", "Math", "cos", "PI", "y", "sin", "z", "setPosition", "isFinished", "delete", "checkVisibility", "setVisible", "visible"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;;;;;;;;;OAChC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CL,U;;yBAKpCM,O,WAFZH,OAAO,CAAC,SAAD,C,gBACPE,iB,UADD,MAEaC,OAFb,SAE6BL,SAF7B,CAE2D;AAAA;AAAA;AAAA,eAEhDM,KAFgD;AAEjB;AAFiB,eAGhDC,UAHgD;AAGjB;AAHiB,eAIhDC,YAJgD;AAIjB;AAJiB,eAKhDC,iBALgD;AAKjB;AALiB,eAO/CC,SAP+C,GAO1B,KAP0B;AAOjB;AAPiB,eAQhDC,eARgD,GAQb,IARa;AAAA,eAShDC,iBATgD,GASX,IATW;AAWvD;AAXuD,eAY/CC,eAZ+C,GAYM,IAAIC,GAAJ,EAZN;AAAA;;AAchDC,QAAAA,SAAS,CAACC,MAAD,EAAwB;AACpC,eAAKH,eAAL,CAAqBI,GAArB,CAAyBD,MAAM,CAACE,UAAhC,EAA4CF,MAA5C;AACH;;AAEMG,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,eAAKP,eAAL,CAAqBQ,OAArB,CAA6B,CAACL,MAAD,EAASM,GAAT,KACzBN,MAAM,CAACG,IAAP,CAAY,IAAZ,EAAkBC,EAAlB,CADJ,EAD0B,CAK1B;;AACA,gBAAMG,OAAO,GAAG,KAAKC,IAAL,CAAUC,WAAV,EAAhB,CAN0B,CAQ1B;;AACA,eAAKnB,KAAL,IAAc,KAAKE,YAAL,GAAoBY,EAAlC;AACA,eAAKb,UAAL,IAAmB,KAAKE,iBAAL,GAAyBW,EAA5C;AAEAM,UAAAA,OAAO,CAACC,GAAR,CAAa,UAAS,KAAKrB,KAAM,iBAAgB,KAAKC,UAAW,EAAjE;AAEA,gBAAMqB,MAAM,GAAG,IAAI3B,IAAJ,CACXsB,OAAO,CAACM,CAAR,GAAY,KAAKvB,KAAL,GAAawB,IAAI,CAACC,GAAL,CAAS,KAAKxB,UAAL,GAAkBuB,IAAI,CAACE,EAAvB,GAA4B,GAArC,CAAb,GAAyDZ,EAD1D,EAEXG,OAAO,CAACU,CAAR,GAAY,KAAK3B,KAAL,GAAawB,IAAI,CAACI,GAAL,CAAS,KAAK3B,UAAL,GAAkBuB,IAAI,CAACE,EAAvB,GAA4B,GAArC,CAAb,GAAyDZ,EAF1D,EAGXG,OAAO,CAACY,CAHG,CAAf;AAKA,eAAKX,IAAL,CAAUY,WAAV,CAAsBR,MAAtB,EAnB0B,CAqB1B;;AACA,eAAKf,eAAL,CAAqBQ,OAArB,CAA6B,CAACL,MAAD,EAASM,GAAT,KAAiB;AAC1C,gBAAIN,MAAM,CAACqB,UAAP,EAAJ,EAAyB;AACrB,mBAAKxB,eAAL,CAAqByB,MAArB,CAA4BhB,GAA5B;AACH;AACJ,WAJD;AAKH;AAED;AACJ;AACA;;;AACWiB,QAAAA,eAAe,GAAS;AAC3B,gBAAM7B,SAAS,GAAG,IAAlB,CAD2B,CACJ;;AACvB,eAAK8B,UAAL,CAAgB9B,SAAhB;AACH;;AAEM8B,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,cAAI,KAAK/B,SAAL,KAAmB+B,OAAvB,EAAgC;AAEhC,eAAK/B,SAAL,GAAiB+B,OAAjB;;AACA,cAAIA,OAAO,IAAI,KAAK9B,eAApB,EAAqC;AACjC,iBAAKA,eAAL;AACH,WAFD,MAEO,IAAI,CAAC8B,OAAD,IAAY,KAAK7B,iBAArB,EAAwC;AAC3C,iBAAKA,iBAAL;AACH;AACJ;;AAhEsD,O", "sourcesContent": ["import { _decorator, Component, Node, Vec3 } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { IMovable, IMoveModifier, eMoveModifier } from './IMovable';\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    public speed: number;                 // 速度\r\n    public speedAngle: number;            // 速度方向 (用角度表示)\r\n    public acceleration: number;          // 加速度\r\n    public accelerationAngle: number;     // 加速度方向 (用角度表示)\r\n\r\n    private isVisible: boolean = false;   // 是否可见\r\n    public onBecomeVisible: Function | null = null;\r\n    public onBecomeInvisible: Function | null = null;\r\n\r\n    // make a map by eMoveModifier\r\n    private activeModifiers: Map<eMoveModifier, IMoveModifier> = new Map();\r\n\r\n    public addSolver(solver: IMoveModifier) {\r\n        this.activeModifiers.set(solver.targetType, solver);\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        this.activeModifiers.forEach((solver, key) => \r\n            solver.tick(this, dt)\r\n        );\r\n\r\n        // 根据移动属性更新位置\r\n        const lastPos = this.node.getPosition();\r\n\r\n        // update speed and acceleration\r\n        this.speed += this.acceleration * dt;\r\n        this.speedAngle += this.accelerationAngle * dt;\r\n\r\n        console.log(`Speed: ${this.speed}, SpeedAngle: ${this.speedAngle}`);\r\n\r\n        const newPos = new Vec3(\r\n            lastPos.x + this.speed * Math.cos(this.speedAngle * Math.PI / 180) * dt,\r\n            lastPos.y + this.speed * Math.sin(this.speedAngle * Math.PI / 180) * dt,\r\n            lastPos.z\r\n        );\r\n        this.node.setPosition(newPos);\r\n\r\n        // remove finished solvers\r\n        this.activeModifiers.forEach((solver, key) => {\r\n            if (solver.isFinished()) {\r\n                this.activeModifiers.delete(key);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * TODO: 如果后续自己写碰撞，这里可以直接查询格子坐标来获取当前是否在屏幕内\r\n     */\r\n    public checkVisibility(): void {\r\n        const isVisible = true;// && this.node.getWorldBoundingBox().intersects(this.node.getScene().getBoundingBox());\r\n        this.setVisible(isVisible);\r\n    }\r\n\r\n    public setVisible(visible: boolean) {\r\n        if (this.isVisible === visible) return;\r\n\r\n        this.isVisible = visible;\r\n        if (visible && this.onBecomeVisible) {\r\n            this.onBecomeVisible();\r\n        } else if (!visible && this.onBecomeInvisible) {\r\n            this.onBecomeInvisible();\r\n        }\r\n    }\r\n}"]}