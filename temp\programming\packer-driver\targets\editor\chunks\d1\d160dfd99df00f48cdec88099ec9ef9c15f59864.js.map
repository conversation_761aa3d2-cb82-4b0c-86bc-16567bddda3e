{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/move/Movable.ts"], "names": ["_decorator", "Component", "Vec3", "ccclass", "property", "executeInEditMode", "Movable", "speed", "speedAngle", "acceleration", "accelerationAngle", "activeModifiers", "Map", "addSolver", "solver", "set", "targetType", "tick", "dt", "for<PERSON>ach", "key", "lastPos", "node", "getPosition", "newPos", "x", "Math", "cos", "PI", "y", "sin", "z", "setPosition", "isFinished", "delete"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;;;;;;;;;OAChC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CL,U;;yBAKpCM,O,WAFZH,OAAO,CAAC,SAAD,C,gBACPE,iB,UADD,MAEaC,OAFb,SAE6BL,SAF7B,CAE2D;AAAA;AAAA;AAAA,eAEhDM,KAFgD;AAEjB;AAFiB,eAGhDC,UAHgD;AAGjB;AAHiB,eAIhDC,YAJgD;AAIjB;AAJiB,eAKhDC,iBALgD;AAKjB;AAEtC;AAPuD,eAQvDC,eARuD,GAQF,IAAIC,GAAJ,EARE;AAAA;;AAUhDC,QAAAA,SAAS,CAACC,MAAD,EAAwB;AACpC,eAAKH,eAAL,CAAqBI,GAArB,CAAyBD,MAAM,CAACE,UAAhC,EAA4CF,MAA5C;AACH;;AAEMG,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,eAAKP,eAAL,CAAqBQ,OAArB,CAA6B,CAACL,MAAD,EAASM,GAAT,KACzBN,MAAM,CAACG,IAAP,CAAY,IAAZ,EAAkBC,EAAlB,CADJ,EAD0B,CAK1B;;AACA,gBAAMG,OAAO,GAAG,KAAKC,IAAL,CAAUC,WAAV,EAAhB,CAN0B,CAQ1B;;AACA,eAAKhB,KAAL,IAAc,KAAKE,YAAL,GAAoBS,EAAlC;AACA,eAAKV,UAAL,IAAmB,KAAKE,iBAAL,GAAyBQ,EAA5C;AAEA,gBAAMM,MAAM,GAAG,IAAItB,IAAJ,CACXmB,OAAO,CAACI,CAAR,GAAY,KAAKlB,KAAL,GAAamB,IAAI,CAACC,GAAL,CAAS,KAAKnB,UAAL,GAAkBkB,IAAI,CAACE,EAAvB,GAA4B,GAArC,CAAb,GAAyDV,EAD1D,EAEXG,OAAO,CAACQ,CAAR,GAAY,KAAKtB,KAAL,GAAamB,IAAI,CAACI,GAAL,CAAS,KAAKtB,UAAL,GAAkBkB,IAAI,CAACE,EAAvB,GAA4B,GAArC,CAAb,GAAyDV,EAF1D,EAGXG,OAAO,CAACU,CAHG,CAAf;AAKA,eAAKT,IAAL,CAAUU,WAAV,CAAsBR,MAAtB,EAjB0B,CAmB1B;;AACA,eAAKb,eAAL,CAAqBQ,OAArB,CAA6B,CAACL,MAAD,EAASM,GAAT,KAAiB;AAC1C,gBAAIN,MAAM,CAACmB,UAAP,EAAJ,EAAyB;AACrB,mBAAKtB,eAAL,CAAqBuB,MAArB,CAA4Bd,GAA5B;AACH;AACJ,WAJD;AAKH;;AAvCsD,O", "sourcesContent": ["import { _decorator, Component, Node, Vec3 } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { IMovable, IMoveModifier, eMoveModifier } from './IMovable';\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    public speed: number;                 // 速度\r\n    public speedAngle: number;            // 速度方向 (用角度表示)\r\n    public acceleration: number;          // 加速度\r\n    public accelerationAngle: number;     // 加速度方向 (用角度表示)\r\n\r\n    // make a map by eMoveModifier\r\n    activeModifiers: Map<eMoveModifier, IMoveModifier> = new Map();\r\n\r\n    public addSolver(solver: IMoveModifier) {\r\n        this.activeModifiers.set(solver.targetType, solver);\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        this.activeModifiers.forEach((solver, key) => \r\n            solver.tick(this, dt)\r\n        );\r\n\r\n        // 根据移动属性更新位置\r\n        const lastPos = this.node.getPosition();\r\n\r\n        // update speed and acceleration\r\n        this.speed += this.acceleration * dt;\r\n        this.speedAngle += this.accelerationAngle * dt;\r\n\r\n        const newPos = new Vec3(\r\n            lastPos.x + this.speed * Math.cos(this.speedAngle * Math.PI / 180) * dt,\r\n            lastPos.y + this.speed * Math.sin(this.speedAngle * Math.PI / 180) * dt,\r\n            lastPos.z\r\n        );\r\n        this.node.setPosition(newPos);\r\n\r\n        // remove finished solvers\r\n        this.activeModifiers.forEach((solver, key) => {\r\n            if (solver.isFinished()) {\r\n                this.activeModifiers.delete(key);\r\n            }\r\n        });\r\n    }\r\n}"]}