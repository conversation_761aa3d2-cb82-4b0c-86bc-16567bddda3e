{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAA6/B,uCAA7/B,EAA2kC,uCAA3kC,EAAopC,uCAAppC,EAAquC,wCAAruC,EAAqzC,wCAArzC,EAA03C,wCAA13C,EAAo8C,wCAAp8C,EAAihD,wCAAjhD,EAA4lD,wCAA5lD,EAAsqD,wCAAtqD,EAA+uD,wCAA/uD,EAAi0D,wCAAj0D,EAAk5D,wCAAl5D,EAAk+D,wCAAl+D,EAAsjE,wCAAtjE,EAAuoE,wCAAvoE,EAAstE,wCAAttE,EAAuyE,wCAAvyE,EAAy3E,wCAAz3E,EAAy8E,wCAAz8E,EAAyhF,wCAAzhF,EAA2mF,wCAA3mF,EAA0rF,wCAA1rF,EAA6wF,wCAA7wF,EAA+1F,wCAA/1F,EAA+6F,wCAA/6F,EAA+/F,wCAA//F,EAAglG,wCAAhlG,EAAiqG,wCAAjqG,EAAwvG,wCAAxvG,EAA60G,wCAA70G,EAAk6G,wCAAl6G,EAAy/G,wCAAz/G,EAAklH,wCAAllH,EAA6qH,wCAA7qH,EAAuwH,wCAAvwH,EAA61H,wCAA71H,EAAm7H,wCAAn7H,EAA4gI,wCAA5gI,EAAqmI,wCAArmI,EAAgsI,wCAAhsI,EAAqxI,wCAArxI,EAA02I,wCAA12I,EAA+7I,wCAA/7I,EAAyhJ,wCAAzhJ,EAA+mJ,wCAA/mJ,EAAssJ,wCAAtsJ,EAA4xJ,wCAA5xJ,EAA03J,wCAA13J,EAAg9J,wCAAh9J,EAAsiK,wCAAtiK,EAAgoK,wCAAhoK,EAAqtK,wCAArtK,EAAkyK,wCAAlyK,EAAw3K,wCAAx3K,EAA28K,wCAA38K,EAAgiL,wCAAhiL,EAAknL,wCAAlnL,EAAqsL,wCAArsL,EAA2xL,wCAA3xL,EAAi3L,wCAAj3L,EAAq8L,wCAAr8L,EAAwhM,wCAAxhM,EAAwmM,wCAAxmM,EAA6rM,wCAA7rM,EAA8wM,wCAA9wM,EAAg2M,wCAAh2M,EAAu7M,wCAAv7M,EAAygN,wCAAzgN,EAA8lN,wCAA9lN,EAAirN,wCAAjrN,EAAywN,wCAAzwN,EAAg2N,wCAAh2N,EAA+6N,wCAA/6N,EAA0gO,wCAA1gO,EAA4lO,wCAA5lO,EAAirO,wCAAjrO,EAAuwO,wCAAvwO,EAAg2O,wCAAh2O,EAAg8O,wCAAh8O,EAA2hP,wCAA3hP,EAAwnP,wCAAxnP,EAAmtP,wCAAntP,EAAyyP,wCAAzyP,EAA+3P,wCAA/3P,EAAk9P,wCAAl9P,EAAoiQ,wCAApiQ,EAAonQ,wCAApnQ,EAA0sQ,wCAA1sQ,EAAkyQ,wCAAlyQ,EAA83Q,wCAA93Q,EAAw9Q,wCAAx9Q,EAAgjR,wCAAhjR,EAAwoR,yCAAxoR,EAAouR,yCAApuR,EAA8zR,yCAA9zR,EAA+5R,yCAA/5R,EAAkgS,yCAAlgS,EAAqmS,yCAArmS,EAAwsS,yCAAxsS,EAAkyS,yCAAlyS,EAAi4S,yCAAj4S,EAAi+S,yCAAj+S,EAAkkT,yCAAlkT,EAA8pT,yCAA9pT,EAAyvT,yCAAzvT,EAAw1T,yCAAx1T,EAA47T,yCAA57T,EAA2hU,yCAA3hU,EAAynU,yCAAznU,EAA4tU,yCAA5tU,EAA8zU,yCAA9zU,EAAq5U,yCAAr5U,EAAm+U,yCAAn+U,EAAgjV,yCAAhjV,EAA6oV,yCAA7oV,EAAsuV,yCAAtuV,EAAo0V,yCAAp0V,EAAi6V,yCAAj6V,EAA+/V,yCAA//V,EAA2lW,yCAA3lW,EAAoqW,yCAApqW,EAA6uW,yCAA7uW,EAA4zW,yCAA5zW,EAAs4W,yCAAt4W,EAAs9W,yCAAt9W,EAAmiX,yCAAniX,EAAunX,yCAAvnX,EAAksX,yCAAlsX,EAA4wX,yCAA5wX,EAAy1X,yCAAz1X,EAAk6X,yCAAl6X,EAA6+X,yCAA7+X,EAA8jY,yCAA9jY,EAAspY,yCAAtpY,EAAqvY,yCAArvY,EAA80Y,yCAA90Y,EAAs6Y,yCAAt6Y,EAA+/Y,yCAA//Y,EAA+lZ,yCAA/lZ,EAA0rZ,yCAA1rZ,EAAixZ,yCAAjxZ,EAAy2Z,yCAAz2Z,EAA+7Z,yCAA/7Z,EAAgha,yCAAhha,EAAoma,yCAApma,EAAksa,yCAAlsa,EAAwxa,yCAAxxa,EAAu3a,yCAAv3a,EAAk9a,yCAAl9a,EAAgjb,yCAAhjb,EAA0ob,yCAA1ob,EAAmub,yCAAnub,EAAyzb,yCAAzzb,EAA+4b,yCAA/4b,EAA8+b,yCAA9+b,EAAokc,yCAApkc,EAAypc,yCAAzpc,EAAyuc,yCAAzuc,EAA+zc,yCAA/zc,EAA+5c,yCAA/5c,EAA2/c,yCAA3/c,EAA+ld,yCAA/ld,EAAwsd,yCAAxsd,EAA2zd,yCAA3zd,EAAm7d,yCAAn7d,EAAuie,yCAAvie,EAAkpe,yCAAlpe,EAAqve,yCAArve,EAA60e,yCAA70e,EAAo6e,yCAAp6e,EAAkgf,yCAAlgf,EAA0lf,yCAA1lf,EAAwrf,yCAAxrf,EAAixf,yCAAjxf,EAA62f,yCAA72f,EAAq8f,yCAAr8f,EAAiigB,yCAAjigB,EAAkmgB,yCAAlmgB,EAA6qgB,yCAA7qgB,EAAgvgB,yCAAhvgB,EAA2zgB,yCAA3zgB,EAA44gB,yCAA54gB,EAAi+gB,yCAAj+gB,EAAsjhB,yCAAtjhB,EAAsohB,yCAAtohB,EAAsthB,yCAAtthB,EAAwyhB,yCAAxyhB,EAA23hB,yCAA33hB,EAAo8hB,yCAAp8hB,EAAwhiB,yCAAxhiB,EAAwmiB,yCAAxmiB,EAAkriB,yCAAlriB,EAA2viB,yCAA3viB,EAAg0iB,yCAAh0iB,EAAq5iB,yCAAr5iB,EAA0/iB,yCAA1/iB,EAAmmjB,yCAAnmjB,EAAssjB,yCAAtsjB,EAAyyjB,yCAAzyjB,EAAo4jB,yCAAp4jB,EAAm+jB,yCAAn+jB,EAAgjkB,yCAAhjkB,EAA6nkB,yCAA7nkB,EAAwskB,yCAAxskB,EAAwxkB,yCAAxxkB,EAAq2kB,yCAAr2kB,EAA+6kB,yCAA/6kB,EAAwglB,yCAAxglB,EAA6llB,yCAA7llB,EAA+qlB,yCAA/qlB,EAAsxlB,yCAAtxlB,EAAs4lB,yCAAt4lB,EAA0+lB,yCAA1+lB,EAAolmB,yCAAplmB,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/Bag.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/BaseInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/DataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/GameLevel.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameFunc.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameIns.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameConfig.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameEnum.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/ResourceList.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/BattleData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/BeanData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/BulletData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EmitterData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EnemyData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EnemyWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/GameMapData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/MainData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/MainStarData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/MapItemData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/StageData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/TrackData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/configData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/event/GameEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BeanManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BossManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/ColliderManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/ConfigDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/CopyPlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/EnemyManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/EventManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameRuleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/HurtEffectManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/ItemManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/LoadManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/LootManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/PlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/PrefabManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/SceneResourceManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/SkillManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/StageManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/WarAttackManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/scenes/Main.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/ParticleComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/AttackPoint.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/BlastComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ColliderArea.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ColliderComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Controller.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/CopyPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ExchangeMap.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/HDSpine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/HDSprite.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ImageSequence.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/NodeMove.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/PfFrameAnim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ScaleComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/TrackComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/UIAnimMethods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/XYFly.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/BoomerangBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/FireBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/LJLaserBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimCircleScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/GridScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/GameMapRun.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/loadingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/ShadowPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossEntity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossHurt.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnit.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnitBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAnim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneRole.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyShootComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainRelifeComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainSkillBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/prefabs/PropEntity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/utils/Helper.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/utils/Tools.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/eventgroup/EventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/Baker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/LevelBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PathBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameInstance.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Anim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Background.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Enemy.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/EnemyBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/GameOver.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/GamePersistNode.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Global.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Goods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/MainGame.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Menu.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Player.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/PlayerBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/AnimFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/EnemyBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/EnemyFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/GameFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/GoodsFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/PlayerBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/gizmos/EmitterArcGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoDrawer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoUtils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/gizmos/index.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/Bootstrap.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/WorldInitializeData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/Entity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/EntityContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/EventAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/EventCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/EventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/Messaging.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/Object.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/System.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/SystemContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/TypeID.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/World.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/index.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/Level.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/LevelEventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/LevelSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/Background.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/BackgroundLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundSpeedAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LevelTimeEventCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LogEventAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/move/DefaultMoveModifier.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/move/IMovable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/move/Movable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/player/PlayerSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/BulletSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/ObjectPool.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Weapon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/WeaponSlot.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/DevLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/DevLoginData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/IPlatformSDK.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/WXLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/leveldata.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/DevLoginUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/LoadingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/UIMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/common/ClickControlUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/components/common/SelectList/uiSelect.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/components/common/SelectList/uiSelectItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/components/common/button/ButtonPlus.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/components/common/button/DragButton.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/components/common/list/List.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/components/common/list/ListItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/BattleUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/BottomUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/ShopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/SkyIslandUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/TalentUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/TopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/EquipLevelUpUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/PlaneTypes.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/PlaneUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagGrid.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/SortTypeDropdown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/Tabs.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/display/EquipDisplay.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/display/MergeDisplay.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}