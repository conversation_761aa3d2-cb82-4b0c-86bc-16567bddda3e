{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Bullet.ts"], "names": ["_decorator", "Component", "EDITOR", "BulletData", "ObjectPool", "Movable", "ccclass", "property", "executeInEditMode", "Bullet", "type", "displayName", "isRunning", "onLoad", "mover", "getComponent", "addComponent", "onCollisionEnter", "other", "self", "onCreate", "speed", "bulletData", "acceleration", "accelerationAngle", "tick", "dt", "console", "log", "destroySelf", "cb", "node", "destroy", "returnNode", "delayDestroy", "scheduleOnce"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CR,U,GAEjD;AACA;AACA;;wBAGaS,M,WAFZH,OAAO,CAAC,QAAD,C,UAIHC,QAAQ,CAAC;AAACG,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,UAGRH,QAAQ,CAAC;AAACG,QAAAA,IAAI;AAAA;AAAA,8BAAL;AAAgBC,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,gBANZH,iB,qBADD,MAEaC,MAFb,SAE4BR,SAF5B,CAEsC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAQ3BW,SAR2B,GAQN,KARM;AAAA;;AAUlCC,QAAAA,MAAM,GAAS;AACX,cAAI,CAAC,KAAKC,KAAV,EAAiB;AACb,iBAAKA,KAAL,GAAa,KAAKC,YAAL;AAAA;AAAA,mCAAb;;AACA,gBAAI,CAAC,KAAKD,KAAV,EAAiB;AACb,mBAAKA,KAAL,GAAa,KAAKE,YAAL;AAAA;AAAA,qCAAb;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,gBAAgB,CAACC,KAAD,EAAcC,IAAd,EAAgC,CAC5C;AACH;;AAEMC,QAAAA,QAAQ,GAAS;AACpB,eAAKR,SAAL,GAAiB,IAAjB;AAEA,eAAKE,KAAL,CAAWO,KAAX,GAAmB,KAAKC,UAAL,CAAgBD,KAAnC;AACA,eAAKP,KAAL,CAAWS,YAAX,GAA0B,KAAKD,UAAL,CAAgBC,YAA1C;AACA,eAAKT,KAAL,CAAWU,iBAAX,GAA+B,KAAKF,UAAL,CAAgBE,iBAA/C;AACH;;AAEMC,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAAA;;AAC1BC,UAAAA,OAAO,CAACC,GAAR,oBAA6B,KAAKhB,SAAlC;AACA,cAAI,CAAC,KAAKA,SAAV,EAAqB,OAFK,CAG1B;;AACA,8BAAKE,KAAL,yBAAYW,IAAZ,CAAiBC,EAAjB;AACH;;AAEMG,QAAAA,WAAW,GAAS;AACvB,eAAKjB,SAAL,GAAiB,KAAjB;;AACA,cAAMkB,EAAE,GAAG,MAAM;AACb,gBAAI5B,MAAJ,EAAY;AACR,mBAAK6B,IAAL,CAAUC,OAAV;AACH,aAFD,MAEO;AACH;AAAA;AAAA,4CAAWC,UAAX,CAAsB,KAAKF,IAA3B;AACH;AACJ,WAND;;AAOA,cAAI,KAAKT,UAAL,CAAgBY,YAAhB,GAA+B,CAAnC,EAAsC;AAClC,iBAAKC,YAAL,CAAkB,MAAM;AACpBL,cAAAA,EAAE;AACL,aAFD,EAEG,KAAKR,UAAL,CAAgBY,YAFnB;AAGH,WAJD,MAIO;AACHJ,YAAAA,EAAE;AACL;AACJ;;AAzDiC,O;;;;;iBAGF,I;;;;;;;iBAGR,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletData } from '../../../Game/data/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { Movable } from '../move/Movable';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n// 子弹 Bullet 伤害计算 \r\n// Weapon -> 发射器, 喷火, 技能武器, 激光\r\n// WeaponSlot -> SetWeapon\r\n@ccclass('Bullet')\r\n@executeInEditMode\r\nexport class Bullet extends Component {\r\n\r\n    @property({type: BulletData})\r\n    public bulletData: BulletData = null;\r\n\r\n    @property({type: Movable, displayName: \"移动组件\"})\r\n    public mover: Movable = null;\r\n\r\n    public isRunning: boolean = false;\r\n\r\n    onLoad(): void {\r\n        if (!this.mover) {\r\n            this.mover = this.getComponent(Movable);\r\n            if (!this.mover) {\r\n                this.mover = this.addComponent(Movable);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * \r\n     */\r\n    onCollisionEnter(other: Node, self: Node): void {\r\n        // 判断另一个node也是子弹或者非子弹, 进行相应处理\r\n    }\r\n    \r\n    public onCreate(): void {\r\n        this.isRunning = true;\r\n\r\n        this.mover.speed = this.bulletData.speed;\r\n        this.mover.acceleration = this.bulletData.acceleration;\r\n        this.mover.accelerationAngle = this.bulletData.accelerationAngle;\r\n    }\r\n\r\n    public tick(dt:number) : void {\r\n        console.log(`Bullet tick : ${this.isRunning}`);\r\n        if (!this.isRunning) return;\r\n        // Update bullet position\r\n        this.mover?.tick(dt);\r\n    }\r\n\r\n    public destroySelf(): void {\r\n        this.isRunning = false;\r\n        const cb = () => {\r\n            if (EDITOR) {\r\n                this.node.destroy();\r\n            } else {\r\n                ObjectPool.returnNode(this.node);\r\n            }\r\n        };\r\n        if (this.bulletData.delayDestroy > 0) {\r\n            this.scheduleOnce(() => {\r\n                cb();\r\n            }, this.bulletData.delayDestroy);\r\n        } else {\r\n            cb();\r\n        }\r\n    }\r\n}\r\n"]}